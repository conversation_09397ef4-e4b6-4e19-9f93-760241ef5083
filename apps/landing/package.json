{"name": "bizzu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "classnames": "^2.5.1", "firebase": "^11.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "3.2.5", "sass": "^1.77.2", "typescript": "^5.2.2", "vite": "^5.2.0"}}