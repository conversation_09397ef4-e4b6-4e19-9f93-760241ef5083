.footer {
  background-color: rgba(70, 11, 132, 0.05);
  padding: 4rem 2rem 2rem;
  color: #18003d;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 3rem;
}

.footerBranding {
  flex: 1 1 300px;
}

.appName {
  font-size: 2em;
  line-height: 1.2em;
  text-decoration: none;
  color: black;
  display: inline-block;
  margin-bottom: 1rem;
}

.appNameText {
  font-weight: bold;
}

.tagline {
  font-size: 1.1rem;
  margin-top: 0.5rem;
  opacity: 0.8;
}

.footerLinks {
  flex: 2 1 600px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.linkGroup {
  flex: 1 1 150px;
}

.linkGroupTitle {
  font-size: 1.2rem;
  margin-bottom: 1.2rem;
  font-weight: 600;
}

.link {
  display: block;
  margin-bottom: 0.8rem;
  text-decoration: none;
  color: inherit;
  opacity: 0.8;
  transition: all 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;

  &:hover {
    opacity: 1;
    transform: translateX(3px);
    color: #aa00ff;
  }
}

.footerBottom {
  max-width: 1200px;
  margin: 3rem auto 0;
  padding-top: 2rem;
  border-top: 1px solid rgba(70, 11, 132, 0.1);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.copyrightSection {
  flex: 1 1 500px;
}

.copyright {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.legalInfo {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-bottom: 0.5rem;
}

.rightsReserved {
  font-size: 0.8rem;
  opacity: 0.7;
}

.socialLinks {
  display: flex;
  gap: 1rem;
}

.socialLink {
  color: #18003d;
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    color: #aa00ff;
    transform: translateY(-3px);
  }
}

@media (max-width: 1000px) {
  .footer {
    padding: 3.5rem 1.8rem 1.8rem;
  }

  .appName {
    font-size: 1.8em;
  }

  .tagline {
    font-size: 1rem;
  }

  .linkGroupTitle {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .link {
    margin-bottom: 0.7rem;
    font-size: 0.95rem;
    width: 100%;
  }

  .footerBottom {
    margin-top: 2.5rem;
    padding-top: 1.8rem;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 3rem 1.5rem 1.5rem;
  }

  .footerContent {
    flex-direction: column;
    gap: 2rem;
  }

  .footerBranding {
    text-align: center;
    flex: 1 1 100px;
  }

  .footerLinks {
    justify-content: space-around;
  }

  .linkGroup {
    flex: 1 1 120px;
    text-align: center;
  }

  .footerBottom {
    flex-direction: column;
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
  }

  .copyrightSection {
    order: 2;
    text-align: center;
    flex: 1 1 200px;
  }

  .socialLinks {
    order: 1;
    justify-content: center;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2.5rem 1rem 1rem;
  }

  .appName {
    font-size: 1.6em;
  }

  .tagline {
    font-size: 0.9rem;
  }

  .footerLinks {
    flex-direction: column;
    gap: 2rem;
  }

  .linkGroup {
    flex: 0 0 auto;
    width: 100%;
  }

  .linkGroupTitle {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .link {
    margin-bottom: 0.6rem;
    font-size: 0.9rem;
  }

  .copyright,
  .legalInfo,
  .rightsReserved {
    font-size: 0.75rem;
  }

  .legalInfo {
    word-break: break-word;
    hyphens: auto;
  }

  .socialLinks {
    margin-bottom: 1.5rem;
  }

  .socialLink svg {
    width: 20px;
    height: 20px;
  }
}

/* Dodatkowe style dla bardzo małych ekranów */
@media (max-width: 360px) {
  .footer {
    padding: 2rem 0.8rem 0.8rem;
  }

  .appName {
    font-size: 1.4em;
  }

  .tagline {
    font-size: 0.8rem;
  }

  .linkGroupTitle {
    font-size: 0.9rem;
  }

  .link {
    font-size: 0.8rem;
  }

  .copyright,
  .legalInfo,
  .rightsReserved {
    font-size: 0.7rem;
  }

  .socialLink {
    padding: 0.3rem;
  }

  .socialLink svg {
    width: 18px;
    height: 18px;
  }
}
