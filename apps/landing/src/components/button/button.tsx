import React, { ButtonHTMLAttributes } from "react";
import classNames from "classnames";
import styles from "./button.module.scss";

export function PrimaryButton({
  className,
  children,
  onClick,
  disabled,
  type,
}: {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: ButtonHTMLAttributes<HTMLButtonElement>["type"];
}) {
  return (
    <button
      className={classNames(styles.primaryButton, className)}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {children}
    </button>
  );
}

export function SecondaryButton({
  className,
  children,
  href = "#",
}: {
  className?: string;
  children: React.ReactNode;
  href?: string;
}) {
  return (
    <a className={classNames(styles.secondaryButton, className)} href={href}>
      {children}
    </a>
  );
}
