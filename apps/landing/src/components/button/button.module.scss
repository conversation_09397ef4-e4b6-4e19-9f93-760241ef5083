.button {
  padding: 0 1.3em;
  font-size: 1.3em;
  cursor: pointer;
  height: 2.5em;
  border-radius: 0.7rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.primaryButton {
  composes: button;

  background-color: #aa00ff;
  background-image: linear-gradient(to right, #7f00ff, #e100ff);
  color: white;
  border: none;

  &:hover {
    background-image: linear-gradient(to right, #9500ff, #ff00e6);
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 8px 15px rgba(170, 0, 255, 0.25);
    animation: pulse 1.5s infinite;
  }

  &:active {
    background-color: #aa00ff;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.secondaryButton {
  composes: button;

  background-color: transparent;
  border: 2px solid #aa00ff;
  color: inherit;

  &:hover {
    background-color: rgba(240, 207, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    background-color: rgba(255, 255, 255, 0.5);
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 8px rgba(170, 0, 255, 0.25);
  }
  50% {
    box-shadow: 0 4px 12px rgba(170, 0, 255, 0.5);
  }
  100% {
    box-shadow: 0 4px 8px rgba(170, 0, 255, 0.25);
  }
}

@media (max-width: 768px) {
  .button {
    width: 80%;
  }
}
