import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

export default function ScrollToHashElement() {
  const location = useLocation();
  const lastHash = useRef("");
  const lastPathname = useRef("");

  // Listen for changes to location
  useEffect(() => {
    // If there is a hash in the URL
    if (location.hash) {
      // Get the hash without the '#'
      const hash = location.hash.substring(1);

      // Save the hash to avoid unnecessary scrolling
      lastHash.current = hash;

      // Find the element with the hash ID
      const element = document.getElementById(hash);

      // If the element exists, scroll to it
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({ behavior: "smooth" });
        }, 100);
      }
    }
    // If there is no hash but the pathname changed
    else if (location.pathname !== lastPathname.current) {
      // Save the pathname to avoid unnecessary scrolling
      lastPathname.current = location.pathname;

      // Scroll to the top of the page
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [location]);

  return null;
}
