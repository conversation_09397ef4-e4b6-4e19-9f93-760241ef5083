import React from "react";
import { Link as RouterLink } from "react-router-dom";
import ColoredText from "../coloredText/coloredText";
import { subscribeToBeta } from "../../utils/betaSubscription";
import styles from "./topbar.module.scss";

function NavLink({
  children,
  to,
  isExternal = false,
}: {
  children: React.ReactNode;
  to: string;
  isExternal?: boolean;
}) {
  // Funkcja do scrollowania strony do góry
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (isExternal || to.startsWith("#")) {
    return (
      <a className={styles.link} href={to}>
        {children}
      </a>
    );
  }

  // Jeśli link nie zawiera fragmentu URL (#), dodajemy onClick, który scrolluje stronę do góry
  if (!to.includes("#")) {
    return (
      <RouterLink className={styles.link} to={to} onClick={scrollToTop}>
        {children}
      </RouterLink>
    );
  }

  // Dla linków z fragmentem URL, nie dodajemy onClick
  return (
    <RouterLink className={styles.link} to={to}>
      {children}
    </RouterLink>
  );
}

function Topbar() {
  return (
    <div className={styles.topbar}>
      <RouterLink className={styles.appName} to="/">
        <span className={styles.appNameText}>
          <ColoredText>bizzu.</ColoredText>
        </span>
        app
      </RouterLink>
      <div className={styles.topbarLinks}>
        <NavLink to="/about-app#features">Zalety</NavLink>
        <NavLink to="/about-app#faq">Najczęstsze pytania</NavLink>
        <button className={styles.link} onClick={subscribeToBeta}>
          <ColoredText>Dołącz do testów</ColoredText>
        </button>
      </div>
    </div>
  );
}

export default Topbar;
