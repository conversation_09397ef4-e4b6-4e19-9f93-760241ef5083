.topbar {
  height: 60px;
  background-color: rgba(70, 11, 132, 0.086);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 0.3em;
  z-index: 1000;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);

  :global(*) {
    scroll-margin-top: 100px;
  }
}

.appName {
  flex: 0 0 auto;
  font-size: 2em;
  line-height: 1.2em;
  padding-left: 0.5em;
  text-decoration: none;
  color: black;
}

.appNameText {
  font-weight: bold;
}

.topbarLinks {
  height: 100%;
  flex: 1 0;
  display: flex;
  align-items: stretch;
  justify-content: flex-end;
  gap: 1em;
}

.link {
  text-decoration: none;
  color: inherit;
  line-height: 1em;
  display: inline-flex;
  align-items: center;
  padding: 0 1em;
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 1000px) {
  .topbar {
    height: 40px;
  }

  .topbarLinks {
    font-size: 0.9em;
    gap: 0.5em;
  }

  .link {
    padding: 0.5em;
  }

  .appName {
    font-size: 1.5em;
  }
}

@media (max-width: 480px) {
  .topbar {
    flex-direction: column;
    height: auto;
    padding: 0.5em 0;
  }

  .topbarLinks {
    width: 100%;
    justify-content: space-around;
  }
}
