import React from "react";
import styles from "./CompanyPages.module.scss";
import ColoredText from "../../components/coloredText/coloredText";
import { PrimaryButton } from "../../components/button/button";

const Contact: React.FC = () => {
  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageContent}>
        <h1 className={styles.pageTitle}>
          <ColoredText>Kontakt</ColoredText>
        </h1>

        <section className={styles.section}>
          <h2 className={styles.sectionTitle}>Skontaktuj się z nami</h2>
          <p>
            Masz pytania dotyczące <ColoredText>bizzu.</ColoredText>app lub
            chcesz dowiedzieć się więcej o naszych usługach? Jesteśmy tutaj, aby
            pomóc! Najszybszym sposobem kontaktu jest wysłanie wiadomości e-mail
            na adres
            <a href="mailto:<EMAIL>" className={styles.emailLink}>
              {" "}
              <EMAIL>
            </a>
            .
          </p>
          <p className={styles.emailInfo}>
            Odpowiadamy na wszystkie wiadomości w ciągu 24 godzin w dni robocze.
            W wiadomości prosimy o podanie:
          </p>
          <ul className={styles.emailInfoList}>
            <li>Imienia i nazwiska</li>
            <li>Nazwy firmy (jeśli dotyczy)</li>
            <li>Krótkiego opisu, w czym możemy pomóc</li>
          </ul>
          <div className={styles.emailCta}>
            <a href="mailto:<EMAIL>">
              <PrimaryButton>Napisz do nas</PrimaryButton>
            </a>
          </div>
        </section>

        <div className={styles.contactSection}>
          <section className={styles.contactInfo}>
            <h3 className={styles.infoTitle}>Dane kontaktowe</h3>

            <div className={styles.infoGroup}>
              <h4>Kontakt</h4>
              <p>
                Email: <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
            </div>

            <div className={styles.infoGroup}>
              <h4>Adres</h4>
              <p>Sprintie Sp. z o.o.</p>
              <p>ul. Złotnicza 13</p>
              <p>64-100 Leszno</p>
            </div>

            <div className={styles.infoGroup}>
              <h4>Dane rejestrowe</h4>
              <p>NIP: 7822888989</p>
              <p>KRS: 0000869282</p>
              <p>REGON: *********</p>
            </div>

            <div className={styles.socialLinks} style={{ display: "none" }}>
              <a
                href="https://www.linkedin.com/company/sprintie/"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="LinkedIn"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
              <a
                href="https://twitter.com/sprintiepl"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Twitter"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                </svg>
              </a>
              <a
                href="https://www.facebook.com/sprintiepl"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Facebook"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                </svg>
              </a>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default Contact;
