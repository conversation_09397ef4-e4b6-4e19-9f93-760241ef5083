.pageContainer {
  padding-top: 80px;
  min-height: 100vh;
  background-color: #fff;
}

.pageContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.pageTitle {
  font-size: 3rem;
  margin-bottom: 2rem;
  text-align: center;
}

.section {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #18003d;
  position: relative;
  padding-bottom: 0.5rem;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-image: linear-gradient(to right, #7f00ff, #e100ff);
  }
}

.valuesList {
  list-style-type: none;
  padding: 0;

  li {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
    position: relative;

    &::before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #aa00ff;
      font-weight: bold;
    }
  }
}

// Contact page styles
.contactSection {
  max-width: 800px;
  margin: 0 auto;
}

.contactInfo {
  background-color: rgba(70, 11, 132, 0.05);
  padding: 2rem;
  border-radius: 10px;
  margin-top: 2rem;
}

.formTitle,
.infoTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #18003d;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 500;
  }

  input,
  textarea {
    padding: 0.8rem;
    border: 1px solid rgba(70, 11, 132, 0.2);
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #aa00ff;
    }
  }
}

.formSubmit {
  margin-top: 1rem;
}

.emailLink {
  color: #aa00ff;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;

  &:hover {
    text-decoration: underline;
  }
}

.emailInfo {
  margin-top: 1.5rem;
  font-weight: 500;
}

.emailInfoList {
  margin: 1rem 0 2rem;
  padding-left: 1.5rem;
  list-style-type: none;

  li {
    margin-bottom: 0.5rem;
    position: relative;

    &::before {
      content: "✓";
      color: #aa00ff;
      position: absolute;
      left: -1.5rem;
    }
  }
}

.emailCta {
  margin: 2rem 0;
  display: flex;
  justify-content: center;

  a {
    text-decoration: none;
  }
}

.infoGroup {
  margin-bottom: 1.5rem;

  h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #aa00ff;
  }

  p {
    margin: 0.3rem 0;
  }

  a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s;

    &:hover {
      color: #aa00ff;
    }
  }
}

.socialLinks {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;

  a {
    color: #18003d;
    opacity: 0.7;
    transition: all 0.2s;

    &:hover {
      opacity: 1;
      color: #aa00ff;
      transform: translateY(-3px);
    }
  }
}

// Responsive styles
@media (max-width: 1000px) {
  .pageTitle {
    font-size: 2.5rem;
  }

  .sectionTitle {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .pageTitle {
    font-size: 2rem;
  }

  .pageContent {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 1.8rem;
  }

  .sectionTitle {
    font-size: 1.4rem;
  }

  .contactInfo {
    padding: 1.5rem;
  }
}
