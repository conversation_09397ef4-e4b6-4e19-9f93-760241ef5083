.headingComponent {
  min-height: calc(100vh - 60px);
}

.mainHeading {
  font-size: 4.5rem;
  margin: 0;
  text-align: center;
  padding: 20vh 25vw 0;
  line-height: 1em;

  @media (max-width: 768px) {
    font-size: 3rem;
    padding: 15vh 10vw 0;
  }
}

.subheading {
  margin: 0;
  text-align: center;
  padding: 5vh 30vw;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;

  @media (max-width: 768px) {
    padding: 3vh 10vw;
    font-size: 1.2rem;
  }
}

.cta {
  margin-bottom: 60px;
  display: flex;
  gap: 1em;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    margin-bottom: 60px;
    flex-direction: column;
    align-items: center;
  }
}

.scrollArrow {
  text-decoration: none;

  &::after {
    content: "↓";
    display: block;
    font-size: 4rem;
    color: #e100ff;
    animation: bounce 2s infinite;
    text-align: center;
    margin-top: 1rem;
  }

  @keyframes bounce {
    0%,
    100% {
      transform: translateY(0);
      opacity: 0.7;
    }
    50% {
      transform: translateY(10px);
      opacity: 1;
    }
  }
}
