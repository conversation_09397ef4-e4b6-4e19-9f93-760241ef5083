import {
  PrimaryButton,
  SecondaryButton,
} from "../../../../components/button/button";
import ColoredText from "../../../../components/coloredText/coloredText";
import { subscribeToBeta } from "../../../../utils/betaSubscription";
import styles from "./heading.module.scss";

function Heading() {
  return (
    <div className={styles.headingComponent}>
      <h1 className={styles.mainHeading}>
        Zapomnij o Excelu. <ColoredText>Zysk pod kontrolą</ColoredText> – w
        jednym miejscu.
      </h1>

      <h2 className={styles.subheading}>
        <p>
          Dla firm wykonawczych, instalatorów i wszystkich, którzy prowadzą
          inwestycje 🏗️
        </p>
      </h2>

      <p className={styles.cta}>
        <PrimaryButton onClick={subscribeToBeta}>
          Dołącz do testów
        </PrimaryButton>
        <SecondaryButton href="#features"><PERSON><PERSON><PERSON><PERSON> jak to działa</SecondaryButton>
      </p>

      <a className={styles.scrollArrow} href="#features"></a>
    </div>
  );
}

export default Heading;
