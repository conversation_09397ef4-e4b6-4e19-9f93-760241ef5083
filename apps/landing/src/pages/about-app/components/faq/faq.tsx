import { useState } from "react";
import styles from "./faq.module.scss";

export default function Faq() {
  return (
    <section id="faq" className={styles.faqSection}>
      <h2 className={styles.sectionHeading}>Najczęściej zadawane pytania</h2>

      <FAQItem
        question="Dla kogo jest Bizzu?"
        answer="Dla małych i średnich firm wykonawczych, instalatorskich i budowlanych. Je<PERSON><PERSON> prowadzisz inwestycje, rozliczasz ludzi i materiały, to jest narzędzie dla Ciebie."
      />

      <FAQItem
        question="<PERSON><PERSON> muszę instalować jakiś program?"
        answer="Nie. Bizzu działa w przeglądarce. Wystarczy laptop, tablet lub telefon z dostępem do internetu."
      />

      <FAQItem
        question="Ile to kosztuje?"
        answer="Na razie nic – zapraszamy do darmowych testów z firmami zerowymi. Docelowy model będzie abonamentowy, z elastyczną ceną zależną od skali."
      />

      <FAQItem
        question="Czy mogę korzystać z Bizzu na telefonie?"
        answer="Tak. Bizzu działa również na smartfonie, bezpośrednio w przeglądarce – masz dostęp do danych z placu budowy, hurtowni albo w aucie."
      />

      <FAQItem
        question="Czy mogę importować faktury / dane z innych systemów?"
        answer="Tak. Planujemy integracje z popularnymi systemami księgowymi i możliwość importu danych."
      />

      <FAQItem
        question="Czy moje dane są bezpieczne?"
        answer="Tak. Dane są przechowywane na serwerach Google Cloud i regularnie zabezpieczane."
      />

      <FAQItem
        question="Czy muszę coś wiedzieć o księgowości lub budżetowaniu?"
        answer="Nie. Bizzu pokazuje wszystko w przejrzysty sposób – widzisz, na czym zarabiasz, a na czym tracisz. Bez Excela i księgowych haseł."
      />
    </section>
  );
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      className={`${styles.faqItem} ${isOpen ? styles.open : ""}`}
      onClick={() => setIsOpen(!isOpen)}
    >
      <h3 className={styles.question}>
        {question}
        <span className={styles.icon}>{isOpen ? "−" : "+"}</span>
      </h3>
      <div
        className={styles.answerWrapper}
        style={{
          maxHeight: isOpen ? "1000px" : "0",
          opacity: isOpen ? 1 : 0,
        }}
      >
        <p className={styles.answer}>{answer}</p>
      </div>
    </div>
  );
}
