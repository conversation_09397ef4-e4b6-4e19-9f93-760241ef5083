.faqSection {
  padding: 5vh 10vw;
  padding-top: 3em; /* <PERSON><PERSON><PERSON><PERSON> padding, aby scroll<PERSON><PERSON> by<PERSON><PERSON> w<PERSON> */
  margin-bottom: 5vh;
  scroll-margin-top: 80px; /* <PERSON><PERSON><PERSON><PERSON> scroll-margin-top, aby uw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON> topbara */

  @media (max-width: 768px) {
    padding: 3vh 5vw;
    padding-top: 2em;
  }
}

.sectionHeading {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 2rem;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #7f00ff, #e100ff);

  @media (max-width: 768px) {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }
}

.faqItem {
  background-color: rgba(240, 207, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    box-shadow: 0 5px 15px rgba(170, 0, 255, 0.15);
  }

  &.open {
    background-color: rgba(240, 207, 255, 0.2);
  }

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
  }
}

.question {
  font-size: 1.3rem;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 0;
  color: #18003d;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.icon {
  font-size: 1.5rem;
  font-weight: 300;
  color: #aa00ff;
  transition: transform 0.3s ease;

  .open & {
    transform: rotate(180deg);
  }
}

.answerWrapper {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.answer {
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0.8rem 0 0;
  color: #333;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}
