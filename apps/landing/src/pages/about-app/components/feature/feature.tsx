import { ReactNode } from "react";
import ColoredText from "../../../../components/coloredText/coloredText";

import styles from "./feature.module.scss";

type FeatureProps = {
  children: ReactNode;
};

type FeatureImageProps = {
  src: string;
};

type FeatureWordingProps = {
  subheading: string;
  heading: string;
  description: string;
};

export default function Feature({ children }: FeatureProps) {
  return <div className={styles.feature}>{children}</div>;
}

export function FeatureImage({ src }: FeatureImageProps) {
  return (
    <div className={styles.image}>
      {" "}
      <img src={src} alt="" />
    </div>
  );
}

export function FeatureWording({
  subheading,
  heading,
  description,
}: FeatureWordingProps) {
  return (
    <div className={styles.wording}>
      <h4 className={styles.subheading}>
        <ColoredText>{subheading}</ColoredText>
      </h4>
      <h1 className={styles.heading}>{heading}</h1>
      <p className={styles.description}>{description}</p>
    </div>
  );
}
