.feature {
  display: flex;
  align-items: center;
  padding: 0 20rem;
}

.image {
  max-width: 80vw;

  > img {
    width: 100%;
  }
}

.wording {
  flex: 0 1 auto;
}

.subheading {
  font-size: 19px;
}

.heading {
  font-size: 30px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .feature {
    flex-direction: column;
    padding: 0 2rem;
    margin-bottom: 3rem;
  }

  .image {
    order: 1;
    margin-bottom: -2rem;
  }

  .wording {
    order: 2;
    text-align: center;
  }
}
