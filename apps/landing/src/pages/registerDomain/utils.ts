const bizzuDomain = (() => {
  const { host } = window.location;
  const parts = host.split(".");

  if (import.meta.env.DEV) {
    return host.split(":")[0];
  }

  if (parts.length === 3) {
    const [, domain, topLevelDomain] = parts;

    return `${domain}.${topLevelDomain}`;
  }

  return host;
})();

export function getInvitationCode() {
  const searchParams = new URLSearchParams(window.location.search);

  return searchParams.get("invitationCode");
}

export function getNavigationLink(subdomain: string) {
  if (import.meta.env.DEV) {
    return `http://${bizzuDomain}:5174`;
  }

  return `https://${subdomain}.${bizzuDomain}`;
}

function setAccessTokenCookie({
  token,
  domain,
}: {
  token: string;
  domain: string;
}) {
  document.cookie = `access-token=${token}; domain=.${domain}; path=/; secure; samesite=lax; max-age=3600`;
}

export function setAuthCookie(accessToken: string) {
  setAccessTokenCookie({ domain: bizzuDomain, token: accessToken });
}

export function validateSubdomain(subdomain: string) {
  return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(subdomain);
}
