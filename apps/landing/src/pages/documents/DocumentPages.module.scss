.pageContainer {
  padding-top: 80px;
  min-height: 100vh;
  background-color: #fff;
}

.pageContent {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.pageTitle {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.lastUpdated {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 2rem;
}

.introduction {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.section {
  margin-bottom: 2.5rem;
}

.sectionTitle {
  font-size: 1.6rem;
  margin-bottom: 1.2rem;
  color: #18003d;
  position: relative;
  padding-bottom: 0.5rem;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-image: linear-gradient(to right, #7f00ff, #e100ff);
  }
}

.subSectionTitle {
  font-size: 1.2rem;
  margin: 1.5rem 0 1rem;
  color: #18003d;
}

ul,
ol {
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;

  li {
    margin-bottom: 0.5rem;
  }
}

.numberedList {
  counter-reset: item;
  list-style-type: none;
  padding-left: 0;

  li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 1rem;
    counter-increment: item;

    &::before {
      content: counter(item) ".";
      position: absolute;
      left: 0;
      font-weight: bold;
      color: #aa00ff;
    }
  }
}

a {
  color: #aa00ff;
  text-decoration: none;
  transition: color 0.2s;
}

.cookiesTable {
  overflow-x: auto;
  margin-bottom: 1.5rem;

  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
  }

  th,
  td {
    padding: 0.8rem;
    text-align: left;
    border-bottom: 1px solid rgba(70, 11, 132, 0.1);
  }

  th {
    background-color: rgba(70, 11, 132, 0.05);
    font-weight: 600;
  }

  tr:hover {
    background-color: rgba(70, 11, 132, 0.02);
  }
}

// Responsive styles
@media (max-width: 1000px) {
  .pageTitle {
    font-size: 2.2rem;
  }

  .sectionTitle {
    font-size: 1.4rem;
  }
}

@media (max-width: 768px) {
  .pageTitle {
    font-size: 1.8rem;
  }

  .pageContent {
    padding: 1.5rem;
  }

  .introduction {
    font-size: 1rem;
  }

  .sectionTitle {
    font-size: 1.3rem;
  }

  .subSectionTitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 1.6rem;
  }

  .sectionTitle {
    font-size: 1.2rem;
  }

  .subSectionTitle {
    font-size: 1rem;
  }

  .numberedList li {
    padding-left: 1.5rem;
  }
}
