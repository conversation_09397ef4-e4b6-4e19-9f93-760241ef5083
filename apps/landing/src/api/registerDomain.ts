import { supabase } from "./supabase";

const errors = {
  subdomain: "<PERSON><PERSON>wa konta jest już <PERSON>, proszę wybrać inną nazwę konta",
  invitation:
    "Zaproszenie do założenia konta wygasło, skontaktuj się z <EMAIL>",
  email_address_invalid: "Podany adres e-mail jest niepoprawny",
  email_not_confirmed: "Ten adres e-mail nie został potwierdzony",
  user_already_exists: "Ten adres e-mail jest już skojażony z innym kontem",
  weak_password: "Hasło jest zbyt słabe. Wymagane jest minimum 6 znaków",
  registerError:
    "Rejestracja nie powiodła się. Skontaktuj się z <EMAIL>",
};

type RegisterDomainPayload = {
  password: string;
  email: string;
  subdomain: string;
  invitationCode: string;
  companyName: string;
};

export async function registerDomain({
  password,
  email,
  subdomain,
  invitationCode,
  companyName,
}: RegisterDomainPayload): Promise<string> {
  const { data: invitationCodeResult, error: invitationCodeError } =
    await checkInvitation(invitationCode);

  if (!invitationCodeResult?.valid || invitationCodeError) {
    return Promise.reject(new Error(errors.invitation));
  }

  const { data: subdomainResult, error: subDomainError } =
    await checkSubdomainAvailability(subdomain);

  if (!subdomainResult?.available || subDomainError) {
    return Promise.reject(new Error(errors.subdomain));
  }

  const { error: signupError, data: signupData } = await signup({
    password,
    email,
    subdomain,
    invitationCode,
    companyName,
  });

  if (signupError) {
    const signupMessage =
      signupError.code && errors[signupError.code as keyof typeof errors];

    return Promise.reject(new Error(signupMessage || errors.registerError));
  }

  const accessToken = signupData.session?.access_token;

  if (!accessToken) {
    return Promise.reject(new Error(errors.registerError));
  }

  return Promise.resolve(accessToken);
}

function checkSubdomainAvailability(subdomain: string) {
  return supabase.rpc("check_subdomain_availability", {
    subdomain,
  });
}

function checkInvitation(invitation: string) {
  return supabase.rpc("validate_invitation_code", {
    invitation_code: invitation,
  });
}

function signup({
  password,
  email,
  subdomain,
  invitationCode,
  companyName,
}: Pick<
  RegisterDomainPayload,
  "password" | "email" | "subdomain" | "invitationCode" | "companyName"
>) {
  return supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        domainCreation: {
          subdomain,
          invitationCode,
          companyName,
        },
      },
    },
  });
}
