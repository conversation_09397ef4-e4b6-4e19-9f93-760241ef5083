import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import Topbar from "./components/topbar/topbar";
import Footer from "./components/footer/footer";
import ScrollToHashElement from "./components/ScrollToHashElement";
import AboutApp from "./pages/about-app/AboutApp";

// Company pages
import AboutUs from "./pages/company/AboutUs";
import Contact from "./pages/company/Contact";

// Document pages
import PrivacyPolicy from "./pages/documents/PrivacyPolicy";
import Terms from "./pages/documents/Terms";
import RODO from "./pages/documents/RODO";
import CookiesPolicy from "./pages/documents/CookiesPolicy";

import RegisterDomain from "./pages/registerDomain/registerDomain";

function App() {
  return (
    <Router>
      <ScrollToHashElement />
      <Topbar />
      <Routes>
        {/* Main routes */}
        <Route path="/" element={<Navigate to="/about-app" replace />} />
        <Route path="/about-app" element={<AboutApp />} />

        {/* Company pages */}
        <Route path="/about" element={<AboutUs />} />
        <Route path="/contact" element={<Contact />} />

        {/* Document pages */}
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/terms" element={<Terms />} />
        <Route path="/gdpr" element={<RODO />} />
        <Route path="/cookies-policy" element={<CookiesPolicy />} />

        <Route path="/register-domain" element={<RegisterDomain />} />
      </Routes>
      <Footer />
    </Router>
  );
}

export default App;
