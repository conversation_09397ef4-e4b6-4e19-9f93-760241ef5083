// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import storybook from "eslint-plugin-storybook";

import js from "@eslint/js";
import stylistic from "@stylistic/eslint-plugin";
import pluginQuery from "@tanstack/eslint-plugin-query";
import eslintPluginImport from "eslint-plugin-import";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import globals from "globals";
import tseslint from "typescript-eslint";

export default tseslint.config({ ignores: ["dist"] }, {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ["**/*.{ts,tsx}"],
    languageOptions: {
        ecmaVersion: 2020,
        globals: globals.browser,
    },
    plugins: {
        "react-hooks": reactHooks,
        "react-refresh": reactRefresh,
        import: eslintPluginImport,
        "@tanstack/query": pluginQuery,
        "@stylistic": stylistic,
    },
    rules: {
        ...reactHooks.configs.recommended.rules,
        "react-refresh/only-export-components": ["warn", { allowConstantExport: true }],

        "@stylistic/curly-newline": ["error", "always"],

        "@stylistic/padding-line-between-statements": ["error", { blankLine: "always", prev: "*", next: "return" }],

        "import/order": [
            "error",
            {
                groups: ["external", "builtin", "internal", ["parent", "sibling", "index"]],
                pathGroups: [
                    {
                        pattern: "react",
                        group: "external",
                        position: "before",
                    },
                    {
                        pattern: "@/**",
                        group: "internal",
                        position: "before",
                    },
                    {
                        pattern: "**/*.css",
                        group: "index",
                        position: "after",
                    },
                    {
                        pattern: "**/*.scss",
                        group: "index",
                        position: "after",
                    },
                ],
                pathGroupsExcludedImportTypes: ["react"],
                alphabetize: {
                    order: "asc",
                    caseInsensitive: true,
                },
                "newlines-between": "never",
            },
        ],
        "no-restricted-imports": [
            "error",
            {
                paths: [
                    {
                        name: "zod",
                        message: "Use zod/v4 instead",
                    },
                ],
            },
        ],
    },
}, storybook.configs["flat/recommended"]);
