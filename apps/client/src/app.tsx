import { createRouter, RouterProvider } from "@tanstack/react-router";
import { queryClient } from "@/utils/queryClient";
import { routeTree } from "./routeTree.gen";

const router = createRouter({
    routeTree,
    context: {
        queryClient,
    },
    defaultPreload: "intent",
    // Since we're using React Query, we don't want loader calls to ever be stale
    // This will ensure that the loader is always called when the route is preloaded or visited
    defaultPreloadStaleTime: 0,
    scrollRestoration: true,
    defaultPendingComponent: () => {
        return <div>Loading...</div>;
    },
});

declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
}

export default function App() {
    return <RouterProvider router={router} />;
}
