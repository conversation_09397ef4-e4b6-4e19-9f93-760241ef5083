import type { AuthError as AuthErrorType, User } from "@supabase/supabase-js";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { UseMutationOptions, UseQueryOptions } from "@tanstack/react-query";
import { forgotPassword, getUser, signIn, signOut, updateUser } from "./auth";
import type { AuthResponse, SignInPayload, UpdateUserPayload } from "./types";

export const queryKeys = {
    getUser: ["auth/getUser"],
};

export function useSignIn(options?: UseMutationOptions<AuthResponse, AuthErrorType, SignInPayload>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: signIn,
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.getUser, data);
            queryClient.invalidateQueries({ queryKey: queryKeys.getUser });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useSignOut(options?: UseMutationOptions<unknown, AuthErrorType, void>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: signOut,
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.getUser, null);
            queryClient.invalidateQueries({ queryKey: queryKeys.getUser });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function getUserQueryConfig(): UseQueryOptions<User> {
    return {
        queryKey: queryKeys.getUser,
        queryFn: getUser,
        retry: false,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes,
    };
}

export function useGetUser() {
    return useQuery(getUserQueryConfig());
}

export function useForgotPassword(options?: UseMutationOptions<void, Error, string>) {
    return useMutation({
        ...options,
        mutationFn: forgotPassword,
    });
}

export function useUpdateUser(options?: UseMutationOptions<void, Error, UpdateUserPayload>) {
    return useMutation({
        ...options,
        mutationFn: updateUser,
    });
}
