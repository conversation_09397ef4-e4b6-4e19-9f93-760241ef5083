import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Skeleton } from "./skeleton";

const meta = {
    title: "UI/Skeleton",
    component: Skeleton,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Skeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => <Skeleton className="w-[100px] h-[20px]" />,
};

export const Circle: Story = {
    render: () => <Skeleton className="h-12 w-12 rounded-full" />,
};

export const Rectangle: Story = {
    render: () => <Skeleton className="h-4 w-[250px]" />,
};

export const Card: Story = {
    render: () => (
        <div className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
            </div>
        </div>
    ),
};

export const ArticleCard: Story = {
    render: () => (
        <div className="w-[350px] space-y-3">
            <Skeleton className="h-[200px] w-full rounded-lg" />
            <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
                <Skeleton className="h-4 w-3/5" />
            </div>
            <div className="flex items-center space-x-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-1">
                    <Skeleton className="h-3 w-[100px]" />
                    <Skeleton className="h-3 w-[80px]" />
                </div>
            </div>
        </div>
    ),
};

export const List: Story = {
    render: () => (
        <div className="w-[300px] space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-2/3" />
                    </div>
                </div>
            ))}
        </div>
    ),
};

export const Table: Story = {
    render: () => (
        <div className="w-[500px] space-y-3">
            <div className="grid grid-cols-4 gap-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
            </div>
            {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="grid grid-cols-4 gap-4">
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-full" />
                </div>
            ))}
        </div>
    ),
};
