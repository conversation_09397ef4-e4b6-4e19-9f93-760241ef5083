import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ScrollArea } from "./scroll-area";
import { Separator } from "./separator";

const meta = {
    title: "UI/ScrollArea",
    component: ScrollArea,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof ScrollArea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <ScrollArea className="h-72 w-48 rounded-md border p-4">
            <div className="pr-4">
                <h4 className="mb-4 text-sm font-medium leading-none">Tags</h4>
                {Array.from({ length: 50 }).map((_, i) => (
                    <div key={i}>
                        <div className="text-sm">Tag {i + 1}</div>
                        {i < 49 && <Separator className="my-2" />}
                    </div>
                ))}
            </div>
        </ScrollArea>
    ),
};

export const HorizontalScroll: Story = {
    render: () => (
        <ScrollArea className="w-96 whitespace-nowrap rounded-md border">
            <div className="flex w-max space-x-4 p-4">
                {Array.from({ length: 20 }).map((_, i) => (
                    <div
                        key={i}
                        className="shrink-0 rounded-md bg-muted p-4 w-32 h-20 flex items-center justify-center"
                    >
                        Item {i + 1}
                    </div>
                ))}
            </div>
        </ScrollArea>
    ),
};

export const LongContent: Story = {
    render: () => (
        <ScrollArea className="h-[200px] w-[350px] rounded-md border p-4">
            <div className="space-y-4">
                <h4 className="text-sm font-medium leading-none">Lorem Ipsum</h4>
                <p className="text-sm text-muted-foreground">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor 
                    incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis 
                    nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                </p>
                <p className="text-sm text-muted-foreground">
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore 
                    eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt 
                    in culpa qui officia deserunt mollit anim id est laborum.
                </p>
                <p className="text-sm text-muted-foreground">
                    Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium 
                    doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore 
                    veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                </p>
                <p className="text-sm text-muted-foreground">
                    Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, 
                    sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
                </p>
                <p className="text-sm text-muted-foreground">
                    Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, 
                    adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et 
                    dolore magnam aliquam quaerat voluptatem.
                </p>
            </div>
        </ScrollArea>
    ),
};

export const ChatMessages: Story = {
    render: () => (
        <ScrollArea className="h-72 w-80 rounded-md border">
            <div className="p-4 space-y-4">
                {Array.from({ length: 20 }).map((_, i) => (
                    <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                        <div className={`max-w-xs rounded-lg p-3 text-sm ${
                            i % 2 === 0 
                                ? 'bg-muted text-muted-foreground' 
                                : 'bg-primary text-primary-foreground'
                        }`}>
                            Message {i + 1}: This is a sample chat message that could be quite long 
                            and might wrap to multiple lines.
                        </div>
                    </div>
                ))}
            </div>
        </ScrollArea>
    ),
};

export const FileList: Story = {
    render: () => (
        <ScrollArea className="h-64 w-64 rounded-md border">
            <div className="p-4">
                <h4 className="mb-4 text-sm font-medium leading-none">Files</h4>
                <div className="space-y-2">
                    {[
                        'document.pdf',
                        'image.jpg',
                        'spreadsheet.xlsx',
                        'presentation.pptx',
                        'archive.zip',
                        'video.mp4',
                        'audio.mp3',
                        'text.txt',
                        'code.js',
                        'style.css',
                        'data.json',
                        'config.xml',
                        'readme.md',
                        'license.txt',
                        'changelog.log'
                    ].map((file, i) => (
                        <div key={i} className="flex items-center justify-between p-2 rounded hover:bg-muted">
                            <span className="text-sm">{file}</span>
                            <span className="text-xs text-muted-foreground">
                                {Math.floor(Math.random() * 1000)}KB
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        </ScrollArea>
    ),
};
