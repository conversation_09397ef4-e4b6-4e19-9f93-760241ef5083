import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Textarea } from "./textarea";
import { Label } from "./label";

const meta = {
    title: "UI/Textarea",
    component: Textarea,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        placeholder: {
            control: { type: "text" },
        },
        disabled: {
            control: { type: "boolean" },
        },
        rows: {
            control: { type: "number" },
        },
    },
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        placeholder: "Type your message here...",
    },
};

export const WithLabel: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="message">Your message</Label>
            <Textarea placeholder="Type your message here..." id="message" />
        </div>
    ),
};

export const WithText: Story = {
    args: {
        value: "This is some sample text in the textarea. You can edit this content.",
        placeholder: "Type your message here...",
    },
};

export const Disabled: Story = {
    args: {
        placeholder: "This textarea is disabled",
        disabled: true,
    },
};

export const WithRows: Story = {
    args: {
        placeholder: "This textarea has a specific number of rows...",
        rows: 6,
    },
};

export const WithDescription: Story = {
    render: () => (
        <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="message-2">Your message</Label>
            <Textarea placeholder="Type your message here..." id="message-2" />
            <p className="text-xs text-muted-foreground">
                Your message will be copied to the support team.
            </p>
        </div>
    ),
};

export const LongContent: Story = {
    args: {
        value: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`,
        rows: 8,
    },
};
