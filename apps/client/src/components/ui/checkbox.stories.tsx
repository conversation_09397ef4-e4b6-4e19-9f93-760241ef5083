import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Checkbox } from "./checkbox";
import { Label } from "./label";

const meta = {
    title: "UI/Checkbox",
    component: Checkbox,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        checked: {
            control: { type: "boolean" },
        },
        disabled: {
            control: { type: "boolean" },
        },
    },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {},
};

export const Checked: Story = {
    args: {
        checked: true,
    },
};

export const Disabled: Story = {
    args: {
        disabled: true,
    },
};

export const DisabledChecked: Story = {
    args: {
        disabled: true,
        checked: true,
    },
};

export const WithLabel: Story = {
    render: () => (
        <div className="flex items-center space-x-2">
            <Checkbox id="terms" />
            <Label htmlFor="terms">Accept terms and conditions</Label>
        </div>
    ),
};

export const WithLabelAndDescription: Story = {
    render: () => (
        <div className="flex items-start space-x-2">
            <Checkbox id="marketing" className="mt-1" />
            <div className="grid gap-1.5 leading-none">
                <Label htmlFor="marketing" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Marketing emails
                </Label>
                <p className="text-xs text-muted-foreground">
                    You agree to receive marketing emails from us.
                </p>
            </div>
        </div>
    ),
};

export const Group: Story = {
    render: () => (
        <div className="space-y-3">
            <div className="flex items-center space-x-2">
                <Checkbox id="option1" />
                <Label htmlFor="option1">Option 1</Label>
            </div>
            <div className="flex items-center space-x-2">
                <Checkbox id="option2" checked />
                <Label htmlFor="option2">Option 2 (checked)</Label>
            </div>
            <div className="flex items-center space-x-2">
                <Checkbox id="option3" disabled />
                <Label htmlFor="option3">Option 3 (disabled)</Label>
            </div>
        </div>
    ),
};
