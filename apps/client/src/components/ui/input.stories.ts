import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Input } from "./input";

const meta = {
    title: "UI/Input",
    component: Input,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
    argTypes: {
        type: {
            control: { type: "select" },
            options: ["text", "email", "password", "number", "tel", "url", "search"],
        },
        placeholder: {
            control: { type: "text" },
        },
        disabled: {
            control: { type: "boolean" },
        },
    },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        placeholder: "Enter text...",
    },
};

export const Email: Story = {
    args: {
        type: "email",
        placeholder: "Enter your email...",
    },
};

export const Password: Story = {
    args: {
        type: "password",
        placeholder: "Enter your password...",
    },
};

export const Number: Story = {
    args: {
        type: "number",
        placeholder: "Enter a number...",
    },
};

export const Disabled: Story = {
    args: {
        placeholder: "Disabled input",
        disabled: true,
    },
};

export const WithValue: Story = {
    args: {
        value: "Pre-filled value",
        placeholder: "This won't show",
    },
};
