import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { MoreH<PERSON>zon<PERSON>, Setting<PERSON> } from "lucide-react";
import { But<PERSON> } from "./button";
import { <PERSON>, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent } from "./card";

const meta = {
    title: "UI/Card",
    component: Card,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardHeader>
                <CardTitle>Card Title</CardTitle>
                <CardDescription>Card description goes here.</CardDescription>
            </CardHeader>
            <CardContent>
                <p>This is the main content of the card.</p>
            </CardContent>
            <CardFooter>
                <Button>Action</Button>
            </CardFooter>
        </Card>
    ),
};

export const WithAction: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardHeader>
                <CardTitle>Settings</CardTitle>
                <CardDescription>Manage your account settings and preferences.</CardDescription>
                <CardAction>
                    <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </CardAction>
            </CardHeader>
            <CardContent>
                <p>Configure your account settings here.</p>
            </CardContent>
        </Card>
    ),
};

export const SimpleCard: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardContent>
                <p>A simple card with just content, no header or footer.</p>
            </CardContent>
        </Card>
    ),
};

export const HeaderOnly: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardHeader>
                <CardTitle>Header Only Card</CardTitle>
                <CardDescription>This card only has a header section.</CardDescription>
            </CardHeader>
        </Card>
    ),
};

export const WithFooterActions: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardHeader>
                <CardTitle>Confirm Action</CardTitle>
                <CardDescription>Are you sure you want to proceed with this action?</CardDescription>
            </CardHeader>
            <CardContent>
                <p>This action cannot be undone. Please confirm your choice.</p>
            </CardContent>
            <CardFooter className="gap-2">
                <Button variant="outline">Cancel</Button>
                <Button>Confirm</Button>
            </CardFooter>
        </Card>
    ),
};

export const ProductCard: Story = {
    render: () => (
        <Card className="w-[350px]">
            <CardHeader>
                <CardTitle>Premium Plan</CardTitle>
                <CardDescription>Perfect for growing businesses</CardDescription>
                <CardAction>
                    <Button variant="ghost" size="icon">
                        <Settings className="h-4 w-4" />
                    </Button>
                </CardAction>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">$29/month</div>
                <ul className="mt-4 space-y-2 text-sm">
                    <li>✓ Unlimited projects</li>
                    <li>✓ Priority support</li>
                    <li>✓ Advanced analytics</li>
                </ul>
            </CardContent>
            <CardFooter>
                <Button className="w-full">Get Started</Button>
            </CardFooter>
        </Card>
    ),
};
