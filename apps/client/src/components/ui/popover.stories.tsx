import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Settings } from "lucide-react";
import { But<PERSON> } from "./button";
import { Input } from "./input";
import { Label } from "./label";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

const meta = {
    title: "UI/Popover",
    component: Popover,
    parameters: {
        layout: "centered",
    },
    tags: ["autodocs"],
} satisfies Meta<typeof Popover>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline">Open popover</Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
                <div className="grid gap-4">
                    <div className="space-y-2">
                        <h4 className="font-medium leading-none">Dimensions</h4>
                        <p className="text-sm text-muted-foreground">
                            Set the dimensions for the layer.
                        </p>
                    </div>
                    <div className="grid gap-2">
                        <div className="grid grid-cols-3 items-center gap-4">
                            <Label htmlFor="width">Width</Label>
                            <Input
                                id="width"
                                defaultValue="100%"
                                className="col-span-2 h-8"
                            />
                        </div>
                        <div className="grid grid-cols-3 items-center gap-4">
                            <Label htmlFor="maxWidth">Max. width</Label>
                            <Input
                                id="maxWidth"
                                defaultValue="300px"
                                className="col-span-2 h-8"
                            />
                        </div>
                        <div className="grid grid-cols-3 items-center gap-4">
                            <Label htmlFor="height">Height</Label>
                            <Input
                                id="height"
                                defaultValue="25px"
                                className="col-span-2 h-8"
                            />
                        </div>
                        <div className="grid grid-cols-3 items-center gap-4">
                            <Label htmlFor="maxHeight">Max. height</Label>
                            <Input
                                id="maxHeight"
                                defaultValue="none"
                                className="col-span-2 h-8"
                            />
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    ),
};

export const WithIcon: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline" size="icon">
                    <Settings className="h-4 w-4" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
                <div className="grid gap-4">
                    <div className="space-y-2">
                        <h4 className="font-medium leading-none">Settings</h4>
                        <p className="text-sm text-muted-foreground">
                            Configure your preferences.
                        </p>
                    </div>
                    <div className="grid gap-2">
                        <div className="flex items-center space-x-2">
                            <input type="checkbox" id="notifications" />
                            <Label htmlFor="notifications">Enable notifications</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <input type="checkbox" id="analytics" />
                            <Label htmlFor="analytics">Share analytics</Label>
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    ),
};

export const SimpleText: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline">Show info</Button>
            </PopoverTrigger>
            <PopoverContent>
                <p className="text-sm">
                    This is a simple popover with just text content. 
                    It can be used to show additional information or help text.
                </p>
            </PopoverContent>
        </Popover>
    ),
};

export const DifferentSides: Story = {
    render: () => (
        <div className="flex gap-4">
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="outline">Top</Button>
                </PopoverTrigger>
                <PopoverContent side="top">
                    <p className="text-sm">Popover on top</p>
                </PopoverContent>
            </Popover>
            
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="outline">Right</Button>
                </PopoverTrigger>
                <PopoverContent side="right">
                    <p className="text-sm">Popover on right</p>
                </PopoverContent>
            </Popover>
            
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="outline">Bottom</Button>
                </PopoverTrigger>
                <PopoverContent side="bottom">
                    <p className="text-sm">Popover on bottom</p>
                </PopoverContent>
            </Popover>
            
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="outline">Left</Button>
                </PopoverTrigger>
                <PopoverContent side="left">
                    <p className="text-sm">Popover on left</p>
                </PopoverContent>
            </Popover>
        </div>
    ),
};

export const WithForm: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <Button>Contact Form</Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
                <div className="grid gap-4">
                    <div className="space-y-2">
                        <h4 className="font-medium leading-none">Contact Us</h4>
                        <p className="text-sm text-muted-foreground">
                            Send us a message and we'll get back to you.
                        </p>
                    </div>
                    <div className="grid gap-2">
                        <div className="grid gap-1">
                            <Label htmlFor="name">Name</Label>
                            <Input id="name" placeholder="Your name" />
                        </div>
                        <div className="grid gap-1">
                            <Label htmlFor="email">Email</Label>
                            <Input id="email" type="email" placeholder="<EMAIL>" />
                        </div>
                        <div className="grid gap-1">
                            <Label htmlFor="message">Message</Label>
                            <textarea 
                                id="message" 
                                placeholder="Your message..."
                                className="min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm"
                            />
                        </div>
                        <Button className="w-full">Send Message</Button>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    ),
};
