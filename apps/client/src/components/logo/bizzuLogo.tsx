type Size = "small" | "medium" | "large";

const sizeMap = {
    small: 160,
    medium: 240,
    large: 320,
};

export function BizzuLogo({ size = "medium", width, height }: { size?: Size; width?: number; height?: number }) {
    const logoWidth = width || sizeMap[size];
    const logoHeight = height || "auto";

    return <img src="/logo-full.svg" alt="bizzu-logo" style={{ width: logoWidth, height: logoHeight }} />;
}
