.container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar content";
    grid-template-columns: auto 1fr;
    grid-template-rows: auto 1fr;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

.header {
    grid-area: header;
    border-bottom: 1px solid var(--gray-4);
    background-color: var(--gray-1);
    width: 100%;
}

.sidebar {
    grid-area: sidebar;
    border-right: 1px solid var(--gray-4);
    background-color: var(--gray-1);

    max-width: 200px;

    // Remove border from header
    margin-top: -1px;
}

.content {
    grid-area: content;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.scrollArea {
    height: 100%;
    width: 100%;
}
