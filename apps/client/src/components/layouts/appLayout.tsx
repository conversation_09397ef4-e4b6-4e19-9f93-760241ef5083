import React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { getCookie } from "@/lib/cookie";
import { Sidebar } from "../navigation";
import { TopBar } from "../topBar";

export function AppLayout({ children }: { children: React.ReactNode }) {
    const sidebarState = getCookie("sidebar_state") === "true";

    return (
        <div className="[--header-height:calc(--spacing(14))]">
            <SidebarProvider className="flex flex-col" defaultOpen={sidebarState}>
                <TopBar />
                <div className="flex flex-1">
                    <Sidebar />
                    <SidebarInset className="p-2">{children}</SidebarInset>
                </div>
            </SidebarProvider>
        </div>
    );
}
