import { Footer } from "@/components/footer";
import { Bizzu<PERSON><PERSON> } from "@/components/logo/bizzuLogo";
import { Card } from "@/components/ui/card";

export function AuthLayout({
    title,
    subtitle,
    children,
}: {
    title: string;
    subtitle: string;
    children: React.ReactNode;
}) {
    return (
        <>
            <div className="w-full max-w-[450px]">
                <div className="mb-9 flex justify-center">
                    <BizzuLogo />
                </div>
                <Card className="shadow-xl">
                    <div className="p-4">
                        <div className="mb-6">
                            <h2 className="text-center text-2xl font-bold">{title}</h2>
                            <h3 className="text-muted-foreground text-center text-sm">{subtitle}</h3>
                        </div>
                        {children}
                    </div>
                </Card>
                <div className="mt-6 flex justify-center">
                    <Footer />
                </div>
            </div>
        </>
    );
}
