import type React from "react";
import { Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

type NotificationType = "error" | "success" | "info";

type NotificationProps = {
    type: NotificationType;
    children: React.ReactNode;
};

export default function Notification({ type, children }: NotificationProps) {
    type AlertVariant = "destructive" | "success" | "info";
    const map: Record<NotificationType, AlertVariant> = {
        error: "destructive",
        success: "success",
        info: "info",
    };

    const variant = map[type];

    return (
        <Alert variant={variant}>
            <Info />
            <AlertDescription>{children}</AlertDescription>
        </Alert>
    );
}
