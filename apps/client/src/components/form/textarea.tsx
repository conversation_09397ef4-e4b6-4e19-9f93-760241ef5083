import type { FieldValues } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";
import type { BaseFormFieldProps } from "./types";

type TextareaFieldProps<T extends FieldValues> = BaseFormFieldProps<T> &
    React.TextareaHTMLAttributes<HTMLTextAreaElement>;

export function TextArea<T extends FieldValues>({ name, label, readonly, ...rest }: TextareaFieldProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <Textarea {...rest} {...field} readOnly={readonly} />
                </FormField>
            )}
        />
    );
}
