import type { FieldValues } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Checkbox as UiCheckbox } from "@/components/ui/checkbox";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";
import type { BaseFormFieldProps } from "./types";

type CheckboxFieldProps<T extends FieldValues> = BaseFormFieldProps<T>;

export function Checkbox<T extends FieldValues>({ name, label, readonly }: CheckboxFieldProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label="" error={fieldState.error?.message}>
                    <div className="flex items-center gap-2">
                        <UiCheckbox
                            checked={field.value}
                            onCheckedChange={readonly ? undefined : field.onChange}
                            disabled={readonly}
                            id={name}
                        />
                        <label htmlFor={name} className="text-sm">
                            {label}
                        </label>
                    </div>
                </FormField>
            )}
        />
    );
}
