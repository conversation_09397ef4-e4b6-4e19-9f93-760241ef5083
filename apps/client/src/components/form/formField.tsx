import type { ReactNode } from "react";

type FormFieldProps = {
    label: string;
    error?: string;
    children: ReactNode;
};

export const FormField = ({ label, error, children }: FormFieldProps) => (
    <div className="flex flex-col gap-1">
        <label className="text-sm font-semibold">{label}</label>
        {children}
        {error && <p className="text-xs text-red-500">{error}</p>}
    </div>
);
