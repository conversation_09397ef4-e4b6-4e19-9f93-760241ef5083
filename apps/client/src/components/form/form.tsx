import type { ReactNode } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Form<PERSON>rovider as RHF<PERSON>rovider, useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import { z } from "zod/v4";
import { FormContext } from "./formContext";
import type { FormEditMode } from "./types";

type FormProps<TSchema extends z.ZodSchema> = {
    schema: TSchema;
    onSubmit: SubmitHandler<z.infer<TSchema>>;
    editMode?: FormEditMode;
    children: ReactNode;
    defaultValues?: z.infer<TSchema>;
};

export function Form<T extends z.ZodSchema>({
    children,
    schema,
    defaultValues,
    onSubmit,
    editMode = "classic",
}: FormProps<T>) {
    const methods = useForm<T>({
        // @ts-expect-error some zod v4 problem
        resolver: zodResolver(schema),
        // @ts-expect-error some zod v4 problem
        defaultValues,
    });

    return (
        <RHFProvider {...methods}>
            <FormContext.Provider
                value={{
                    editMode,
                    control: methods.control,
                    errors: methods.formState.errors,
                }}
            >
                <form
                    onSubmit={
                        // @ts-expect-error some zod v4 problem
                        methods.handleSubmit(onSubmit)
                    }
                >
                    {children}
                </form>
            </FormContext.Provider>
        </RHFProvider>
    );
}
