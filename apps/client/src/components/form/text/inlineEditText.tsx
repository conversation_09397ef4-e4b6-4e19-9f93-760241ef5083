import { useEffect, useRef, useState } from "react";
import { Check, Edit3, X } from "lucide-react";
import type { FieldValues } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useFormContext } from "../formContext";
import type { BaseFormFieldProps } from "../types";

export type InlineEditTextFieldProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    onSave?: () => Promise<void> | void;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function InlineEditText<T extends FieldValues>({
    startAdornment,
    endAdornment,
    field,
    readonly,
    onSave,
    ...rest
}: InlineEditTextFieldProps<T> & { field: any }) {
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [originalValue, setOriginalValue] = useState<string>("");
    const [isDragging, setIsDragging] = useState(false);
    const mouseStartPos = useRef<{ x: number; y: number } | null>(null);
    const clickTimeout = useRef<NodeJS.Timeout | null>(null);
    const editingRef = useRef<HTMLDivElement>(null);
    const { errors } = useFormContext<T>();

    const handleClick = () => {
        if (readonly || isDragging) return;

        // Clear any existing timeout
        if (clickTimeout.current) {
            clearTimeout(clickTimeout.current);
            clickTimeout.current = null;
        }

        // Delay the edit action to allow double-click to cancel it
        clickTimeout.current = setTimeout(() => {
            setOriginalValue(field.value || "");
            setIsEditing(true);
            clickTimeout.current = null;
        }, 300); // delay to detect double-click
    };

    const handleDoubleClick = (e: React.MouseEvent) => {
        e.preventDefault();

        // Cancel the pending single-click edit action
        if (clickTimeout.current) {
            clearTimeout(clickTimeout.current);
            clickTimeout.current = null;
        }

        // Select the text content
        const selection = window.getSelection();
        const range = document.createRange();
        const textNode = e.currentTarget.querySelector("span");

        if (textNode && selection && field.value) {
            range.selectNodeContents(textNode);
            selection.removeAllRanges();
            selection.addRange(range);
        }
    };

    const handleMouseDown = (e: React.MouseEvent) => {
        setIsDragging(false);
        mouseStartPos.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        if (!mouseStartPos.current) return;

        const deltaX = Math.abs(e.clientX - mouseStartPos.current.x);
        const deltaY = Math.abs(e.clientY - mouseStartPos.current.y);
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Only consider it dragging if moved more than 5 pixels
        if (distance > 5) {
            setIsDragging(true);
        }
    };

    const handleMouseUp = () => {
        // Reset dragging state after a short delay to allow click to process
        setTimeout(() => {
            setIsDragging(false);
            mouseStartPos.current = null;
        }, 0);
    };

    const handleCancel = () => {
        field.onChange(originalValue); // Reset to original value
        setIsEditing(false);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (isEditing && editingRef.current && !editingRef.current.contains(event.target as Node)) {
                handleCancel();
            }
        };

        if (isEditing) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isEditing, handleCancel]);

    const handleSave = async () => {
        if (isSaving) return;
        setIsSaving(true);
        try {
            await onSave?.();
            if (!errors || Object.keys(errors).length === 0) {
                setIsEditing(false);
            }
        } finally {
            setIsSaving(false);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
            e.preventDefault();
            handleSave();
        } else if (e.key === "Escape") {
            handleCancel();
        }
    };

    if (isEditing) {
        return (
            <div ref={editingRef} className="relative flex items-center gap-1">
                {startAdornment && (
                    <div className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 z-10 flex items-center pl-3">
                        {startAdornment}
                    </div>
                )}
                <Input
                    {...rest}
                    {...field}
                    autoFocus
                    onKeyDown={handleKeyDown}
                    className={[startAdornment ? "pl-9" : "", "pr-16"].filter(Boolean).join(" ")}
                />
                <div className="absolute inset-y-0 right-1 flex items-center gap-1">
                    <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={handleSave}
                        disabled={isSaving}
                        className="h-7 w-7 p-0"
                    >
                        <Check className="h-3 w-3" />
                    </Button>
                    <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={handleCancel}
                        disabled={isSaving}
                        className="h-7 w-7 p-0"
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            </div>
        );
    }

    const displayText = field.value || rest.placeholder || "Click to edit";
    const hasValue = Boolean(field.value);

    return (
        <div
            className={`group relative rounded-md border border-transparent px-3 py-2 text-sm transition-colors ${
                readonly ? "cursor-default" : "hover:border-input hover:bg-muted/50 cursor-pointer"
            }`}
            onClick={handleClick}
            onDoubleClick={handleDoubleClick}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
        >
            <div className="flex items-center justify-between">
                <div className="flex min-w-0 flex-1 items-center gap-2">
                    {startAdornment && (
                        <div className="text-muted-foreground flex flex-shrink-0 items-center">{startAdornment}</div>
                    )}
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <span className={`truncate ${hasValue ? "" : "text-muted-foreground"}`}>{displayText}</span>
                        </TooltipTrigger>
                        {hasValue && displayText.length > 30 && (
                            <TooltipContent>
                                <p className="max-w-xs break-words">{displayText}</p>
                            </TooltipContent>
                        )}
                    </Tooltip>
                    {endAdornment && (
                        <div className="text-muted-foreground flex flex-shrink-0 items-center">{endAdornment}</div>
                    )}
                </div>
                {!readonly && (
                    <Edit3 className="text-muted-foreground h-3 w-3 flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100" />
                )}
            </div>
        </div>
    );
}
