import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import { DollarSign, Mail, Search, User } from "lucide-react";
import { z } from "zod/v4";
import { Form } from "../form";
import { Text } from "./text";

// Mock form data type for stories
type MockFormData = {
    email: string;
    username: string;
    price: string;
    search: string;
    description: string;
};

// Mock schema for form validation
const mockSchema = z.object({
    email: z.string().email("Please enter a valid email address").optional(),
    username: z.string().min(3, "Username must be at least 3 characters").optional(),
    price: z.string().min(1, "Price is required").optional(),
    search: z.string().optional(),
    description: z.string().optional(),
});

const meta = {
    title: "Form/Text",
    component: Text,
    parameters: {
        layout: "centered",
    },
    argTypes: {
        name: {
            control: "select",
            options: ["email", "username", "price", "search", "description"],
            description: "Field name for form binding",
        },
        label: {
            control: "text",
            description: "Label text displayed above the input",
        },
        type: {
            control: "select",
            options: ["text", "email", "password", "number", "search", "tel", "url"],
            description: "HTML input type",
        },
        placeholder: {
            control: "text",
            description: "Placeholder text shown when input is empty",
        },
        readonly: {
            control: "boolean",
            description: "Whether the field is readonly",
        },
        disabled: {
            control: "boolean",
            description: "Whether the field is disabled",
        },
        required: {
            control: "boolean",
            description: "Whether the field is required",
        },
        startAdornment: {
            control: false,
            description: "React node to display at the start of the input",
        },
        endAdornment: {
            control: false,
            description: "React node to display at the end of the input",
        },
    },
    decorators: [
        (Story, context) => (
            <Form
                editMode={context.parameters?.editMode || "classic"}
                defaultValues={context.parameters?.defaultValues || {}}
                schema={mockSchema}
                onSubmit={() => {}}
            >
                <Story />
            </Form>
        ),
    ],
} satisfies Meta<typeof Text<MockFormData>>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username",
        placeholder: "Enter your username",
    },
};

export const WithEmail: Story = {
    args: {
        name: "email" as keyof MockFormData,
        label: "Email Address",
        type: "email",
        placeholder: "Enter your email",
        startAdornment: <Mail className="h-4 w-4" />,
    },
};

export const WithStartAdornment: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username",
        placeholder: "Enter your username",
        startAdornment: <User className="h-4 w-4" />,
    },
};

export const WithEndAdornment: Story = {
    args: {
        name: "price" as keyof MockFormData,
        label: "Price",
        type: "number",
        placeholder: "0.00",
        endAdornment: <span className="text-xs font-medium">USD</span>,
    },
};

export const WithBothAdornments: Story = {
    args: {
        name: "price" as keyof MockFormData,
        label: "Price",
        type: "number",
        placeholder: "0.00",
        startAdornment: <DollarSign className="h-4 w-4" />,
        endAdornment: <span className="text-xs font-medium">USD</span>,
    },
};

export const SearchField: Story = {
    args: {
        name: "search" as keyof MockFormData,
        label: "Search",
        type: "search",
        placeholder: "Search...",
        startAdornment: <Search className="h-4 w-4" />,
    },
};

export const Required: Story = {
    args: {
        name: "email" as keyof MockFormData,
        label: "Email Address",
        type: "email",
        placeholder: "Enter your email",
        required: true,
        startAdornment: <Mail className="h-4 w-4" />,
    },
};

export const Disabled: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username",
        placeholder: "Enter your username",
        disabled: true,
        startAdornment: <User className="h-4 w-4" />,
    },
    parameters: {
        defaultValues: {
            username: "disabled_user",
        },
    },
};

export const Readonly: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username",
        placeholder: "Enter your username",
        readonly: true,
        startAdornment: <User className="h-4 w-4" />,
    },
    parameters: {
        defaultValues: {
            username: "readonly_user",
        },
    },
};

// Inline Edit Mode Stories
export const InlineEditDefault: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username (Inline Edit)",
        placeholder: "Click to edit",
        startAdornment: <User className="h-4 w-4" />,
    },
    parameters: {
        editMode: "inline",
        defaultValues: {
            username: "john_doe",
        },
    },
};

export const InlineEditWithEmail: Story = {
    args: {
        name: "email" as keyof MockFormData,
        label: "Email Address (Inline Edit)",
        type: "email",
        placeholder: "Click to edit email",
        startAdornment: <Mail className="h-4 w-4" />,
    },
    parameters: {
        editMode: "inline",
        defaultValues: {
            email: "<EMAIL>",
        },
    },
};

export const InlineEditReadonly: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Username (Readonly Inline)",
        placeholder: "Double-click to select",
        readonly: true,
        startAdornment: <User className="h-4 w-4" />,
    },
    parameters: {
        editMode: "inline",
        defaultValues: {
            username: "readonly_user",
        },
    },
};

export const InlineEditEmpty: Story = {
    args: {
        name: "description" as keyof MockFormData,
        label: "Description (Empty)",
        placeholder: "Click to add description",
    },
    parameters: {
        editMode: "inline",
        defaultValues: {},
    },
};

// // Comparison Stories
export const EditModeComparison: Story = {
    args: {
        name: "username" as keyof MockFormData,
        label: "Edit Mode Comparison",
    },
    render: () => (
        <div className="w-96 space-y-8">
            <div>
                <h3 className="mb-4 text-lg font-semibold">Classic Edit Mode</h3>
                <Form
                    editMode="classic"
                    defaultValues={{ username: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="username"
                        label="Username"
                        placeholder="Enter your username"
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
            <div>
                <h3 className="mb-4 text-lg font-semibold">Inline Edit Mode - Editable</h3>
                <Form
                    editMode="inline"
                    defaultValues={{ username: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="username"
                        label="Username"
                        placeholder="Click to edit"
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
            <div>
                <h3 className="mb-4 text-lg font-semibold">Inline Edit Mode - Readonly</h3>
                <Form
                    editMode="inline"
                    defaultValues={{ username: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="username"
                        label="Username"
                        placeholder="Double-click to select"
                        readonly
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
        </div>
    ),
};

// // Multiple Adornment Examples
// export const MultipleAdornmentTypes: Story = {
//     args: {
//         name: "email" as keyof MockFormData,
//         label: "Multiple Adornment Examples",
//     },
//     render: () => (
//         <div className="space-y-4">
//             <Text<MockFormData>
//                 name="email"
//                 label="Email with Icon"
//                 type="email"
//                 placeholder="<EMAIL>"
//                 startAdornment={<Mail className="h-4 w-4" />}
//             />
//             <Text<MockFormData>
//                 name="price"
//                 label="Price with Symbol and Unit"
//                 type="number"
//                 placeholder="0.00"
//                 startAdornment={<DollarSign className="h-4 w-4" />}
//                 endAdornment={<span className="text-xs font-medium">USD</span>}
//             />
//             <Text<MockFormData>
//                 name="search"
//                 label="Search with Action"
//                 type="search"
//                 placeholder="Search products..."
//                 startAdornment={<Search className="h-4 w-4" />}
//                 endAdornment={<button className="text-xs font-medium text-blue-600 hover:text-blue-800">Clear</button>}
//             />
//         </div>
//     ),
// };

// // All States Example
// export const AllStates: Story = {
//     args: {
//         name: "username" as keyof MockFormData,
//         label: "All States Example",
//     },
//     render: () => (
//         <div className="space-y-4">
//             <Text<MockFormData> name="username" label="Normal State" placeholder="Enter username" />
//             <Text<MockFormData>
//                 name="email"
//                 label="Required Field"
//                 type="email"
//                 placeholder="Enter email"
//                 required
//                 startAdornment={<Mail className="h-4 w-4" />}
//             />
//             <Text<MockFormData>
//                 name="price"
//                 label="Disabled"
//                 type="number"
//                 placeholder="0.00"
//                 disabled={true}
//                 startAdornment={<DollarSign className="h-4 w-4" />}
//             />
//             <Text<MockFormData>
//                 name="search"
//                 label="Readonly"
//                 readonly={true}
//                 placeholder="Readonly field"
//                 startAdornment={<Search className="h-4 w-4" />}
//             />
//         </div>
//     ),
// };

// // Long Text and Truncation (for inline mode)
// export const LongTextTruncation: Story = {
//     args: {
//         name: "description" as keyof MockFormData,
//         label: "Long Text Truncation",
//         placeholder: "Click to edit",
//     },
//     parameters: {
//         editMode: "inline",
//         defaultValues: {
//             description:
//                 "This is a very long text that should be truncated in inline mode and show a tooltip when hovered",
//         },
//     },
// };

// // Interactive Behavior Demo
// export const InteractiveBehaviorDemo: Story = {
//     args: {
//         name: "username" as keyof MockFormData,
//         label: "Interactive Behavior Demo",
//     },
//     render: () => (
//         <div className="space-y-6">
//             <div className="space-y-2">
//                 <h4 className="font-medium">Editable (try single-click, double-click, drag selection)</h4>
//                 <Form
//                     editMode="inline"
//                     defaultValues={{ username: "Click me to edit" }}
//                     schema={mockSchema}
//                     onSubmit={() => {}}
//                 >
//                     <Text<MockFormData>
//                         name="username"
//                         label="Username"
//                         placeholder="Enter username"
//                         startAdornment={<User className="h-4 w-4" />}
//                     />
//                 </Form>
//             </div>
//             <div className="space-y-2">
//                 <h4 className="font-medium">Readonly (only double-click and drag selection work)</h4>
//                 <Form
//                     editMode="inline"
//                     defaultValues={{ email: "<EMAIL>" }}
//                     schema={mockSchema}
//                     onSubmit={() => {}}
//                 >
//                     <Text<MockFormData>
//                         name="email"
//                         label="Email"
//                         placeholder="Enter email"
//                         startAdornment={<Mail className="h-4 w-4" />}
//                         readonly
//                     />
//                 </Form>
//             </div>
//             <div className="space-y-2">
//                 <h4 className="font-medium">Empty field (shows placeholder)</h4>
//                 <Form editMode="inline" defaultValues={{}} schema={mockSchema} onSubmit={() => {}}>
//                     <Text<MockFormData> name="description" label="Description" placeholder="Click to add description" />
//                 </Form>
//             </div>
//         </div>
//     ),
// };
