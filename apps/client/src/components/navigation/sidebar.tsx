import { SidebarContent, <PERSON><PERSON><PERSON>ooter, SidebarTrigger, Sidebar as UISidebar } from "@/components/ui/sidebar";
import { Navigation } from "./navigation";

export function Sidebar() {
    return (
        <UISidebar
            className="top-(--header-height) h-[calc(100svh-var(--header-height))]!"
            variant="inset"
            collapsible="icon"
        >
            <SidebarContent>
                <Navigation />
            </SidebarContent>
            <SidebarFooter>
                <SidebarTrigger />
            </SidebarFooter>
        </UISidebar>
    );
}
