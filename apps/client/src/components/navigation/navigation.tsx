import { Link, useRouterState } from "@tanstack/react-router";
import { Home, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";
import { SidebarGroup, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";

export function Navigation() {
    const { t } = useTranslation();
    const location = useRouterState({ select: (s) => s.location.pathname });

    const items = [
        { to: "/", label: t("navigation.home"), Icon: Home },
        { to: "/settings", label: t("navigation.settings"), Icon: Settings },
    ];

    return (
        <SidebarGroup>
            <SidebarMenu>
                {items.map((item) => (
                    <SidebarMenuItem key={item.label}>
                        <SidebarMenuButton asChild isActive={location === item.to} tooltip={item.label}>
                            <Link to={item.to}>
                                <item.Icon />
                                <span>{item.label}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}
