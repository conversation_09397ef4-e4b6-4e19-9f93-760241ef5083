import { useTranslation } from "react-i18next";
import { useGetUser } from "@/api/auth";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useSignOut } from "@/hooks/useSignOut";

export function User() {
    const { t } = useTranslation();
    const { data } = useGetUser();
    const signOut = useSignOut();

    const avatarLetter = data?.email?.charAt(0).toUpperCase() || "?";

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" aria-label={t("logout")}>
                    <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-electric-violet-500 text-white">{avatarLetter}</AvatarFallback>
                    </Avatar>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-fit p-2">
                <Button onClick={() => signOut()} className="w-full">
                    {t("logout")}
                </Button>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
