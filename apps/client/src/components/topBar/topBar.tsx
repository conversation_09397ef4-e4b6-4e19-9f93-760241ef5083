import { Link } from "@tanstack/react-router";
import {
    Bread<PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    B<PERSON><PERSON><PERSON>bList,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { BizzuLogo } from "../logo";
import { User } from "./user";

export function TopBar() {
    return (
        <header className="bg-sidebar sticky top-0 z-50 flex w-full items-center">
            <div className="flex h-(--header-height) w-full items-center gap-2 px-4">
                <BizzuLogo width={110} />
                <div className="flex-1 px-5">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link to={"/"}>Home</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link to={"/"}>To Do</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link to={"/"}>Nested paths</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
                <User />
            </div>
        </header>
    );
}
