import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { getUserQueryConfig } from "@/api/auth";
import { AppLayout } from "@/components/layouts/appLayout";

export const Route = createFileRoute("/_authenticated")({
    beforeLoad: async ({ context, location }) => {
        const { queryClient } = context;
        try {
            const user = await queryClient.fetchQuery(getUserQueryConfig());

            if (user) {
                // user found we can stay on authenticated routes
                return;
            }
        } catch {
            throw redirect({ to: "/login", search: { redirect: location.pathname } });
        }
    },
    component: () => (
        <AppLayout>
            <Outlet />
        </AppLayout>
    ),
});
