import { createFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { useForgotPassword } from "@/api/auth/hooks";
import { AuthLayout } from "@/components/layouts/authLayout";
import { Notification } from "@/components/notification";
import { ForgotPasswordForm } from "@/features/forgotPasswordForm";
import type { ForgotPasswordFormData } from "@/features/forgotPasswordForm";

export const Route = createFileRoute("/_public/forgot-password")({
    validateSearch: z.object({
        redirect: z.string().optional().catch(""),
    }),
    component: ForgotPasswordPage,
});

function ForgotPasswordPage() {
    const { t } = useTranslation();
    const { mutateAsync: forgotPassword, isPending, error, isSuccess } = useForgotPassword();

    const handleSubmit = async (data: ForgotPasswordFormData) => {
        try {
            await forgotPassword(data.email);
        } catch {
            /* empty */
        }
    };

    return (
        <AuthLayout title={t("forgotPasswordForm.title")} subtitle={t("forgotPasswordForm.subtitle")}>
            {isSuccess && <Notification type="success">{t("forgotPasswordForm.resetLinkSent")}</Notification>}
            {!isSuccess && <ForgotPasswordForm onSubmit={handleSubmit} error={error?.message} isLoading={isPending} />}
        </AuthLayout>
    );
}
