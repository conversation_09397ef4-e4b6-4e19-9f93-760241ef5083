import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { useSignIn } from "@/api/auth";
import { AuthLayout } from "@/components/layouts/authLayout";
import LoginForm from "@/features/loginForm/loginForm";
import type { LoginForm as LoginFormType } from "@/features/loginForm/loginForm";
import { getSubdomain } from "@/utils/getSubdomain";

export const Route = createFileRoute("/_public/login")({
    validateSearch: z.object({
        redirect: z.string().optional().catch(""),
    }),
    component: LoginPage,
});

function LoginPage() {
    const router = useRouter();
    const { t } = useTranslation();
    const { mutateAsync: login, error, isPending, isSuccess } = useSignIn();

    const search = Route.useSearch();

    const handleLogin = async (data: LoginFormType) => {
        try {
            await login(data);

            router.navigate({ to: search.redirect || "/" });
        } catch {
            /* empty */
        }
    };

    return (
        <AuthLayout
            title={t("loginForm.title")}
            subtitle={t("loginForm.subtitle", { subdomain: `${getSubdomain()}.bizzu.app` })}
        >
            <LoginForm onLogin={handleLogin} error={error} isPending={isPending || isSuccess} />
        </AuthLayout>
    );
}
