import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { useUpdateUser } from "@/api/auth/hooks";
import { AuthLayout } from "@/components/layouts/authLayout";
import { FullPageCenterContent } from "@/components/layouts/fullPageCenterContent";
import { PasswordResetForm } from "@/features/passwordResetForm";
import type { PasswordResetFormData } from "@/features/passwordResetForm";

export const Route = createFileRoute("/_authenticated/password-reset")({
    validateSearch: z.object({
        access_token: z.string().optional(),
        refresh_token: z.string().optional(),
        type: z.string().optional(),
    }),
    component: PasswordResetPage,
});

function PasswordResetPage() {
    const router = useRouter();
    const { t } = useTranslation();
    const { mutateAsync: updateUser, isPending, error } = useUpdateUser();

    const handleSubmit = async (data: PasswordResetFormData) => {
        try {
            await updateUser({ password: data.password });

            router.navigate({
                to: "/",
            });
        } catch {
            /* empty */
        }
    };

    return (
        <FullPageCenterContent>
            <AuthLayout title={t("passwordResetForm.title")} subtitle={t("passwordResetForm.subtitle")}>
                <PasswordResetForm onSubmit={handleSubmit} error={error?.message} isLoading={isPending} />
            </AuthLayout>
        </FullPageCenterContent>
    );
}
