import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { getUserQueryConfig } from "@/api/auth";
import { FullPageCenterContent } from "@/components/layouts/fullPageCenterContent";

export const Route = createFileRoute("/_public")({
    beforeLoad: async ({ context }) => {
        const { queryClient } = context;
        let user;
        try {
            user = await queryClient.fetchQuery(getUserQueryConfig());
        } catch {
            // we don't have user, do nothing, stay on public routes
        }

        if (user) {
            throw redirect({ to: "/", replace: true });
        }
    },
    component: () => {
        return (
            <FullPageCenterContent>
                <Outlet />
            </FullPageCenterContent>
        );
    },
});
