import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { z } from "zod/v4";
import commonEn from "@/locales/en/common.json";
import commonPl from "@/locales/pl/common.json";

export const defaultNS = "common";
export const resources = {
    pl: {
        common: commonPl,
    },
    en: {
        common: commonEn,
    },
} as const;

i18n.use(initReactI18next).init({
    lng: "pl",
    ns: ["common"],
    defaultNS,
    resources,
});

z.config({
    localeError: (issue) => {
        switch (issue.code) {
            case "too_big": {
                return i18n.t("common:errors.tooBig", { count: Number(issue.maximum) });
            }
            case "too_small": {
                return i18n.t("common:errors.tooSmall", { count: Number(issue.minimum) });
            }
            case "custom": {
                return i18n.t("common:errors.required");
            }
            case "invalid_format": {
                switch (issue.format) {
                    case "email": {
                        return i18n.t("common:errors.email");
                    }
                }
            }
        }
    },
});
