/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run "npm run dev" in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run "npm run deploy" to publish your worker
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

function determineTargetHost(hostname) {
  const parts = hostname.split(".");

  const subdomain = parts.length === 3 && parts[0];
  const domain = `${parts[parts.length - 2]}.${parts[parts.length - 1]}`;

  console.log(subdomain, domain);

  if (domain === "bizzu.dev") {
    return subdomain ? "bizzu-client-dev.web.app" : "bizzu-dev.web.app";
  } else if (domain === "bizzu.app") {
    return subdomain ? "bizzu-client-f5a0c.web.app" : "bizzu.web.app";
  }

  return undefined;
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const hostname = url.hostname;

    url.hostname = determineTargetHost(hostname);

    return fetch(url.toString(), request);
  },
};
