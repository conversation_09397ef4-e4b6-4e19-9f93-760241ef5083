name: ESLint Check - Client App

on:
  pull_request:
    branches: [main, develop]
    types: [opened, synchronize, reopened]
    paths:
      - "apps/client/**"

jobs:
  eslint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "apps/client/package-lock.json"

      - name: Install dependencies
        working-directory: ./apps/client
        run: npm ci

      - name: Run ESLint
        working-directory: ./apps/client
        run: npm run lint

      - name: Build
        working-directory: ./apps/client
        run: npm run build
