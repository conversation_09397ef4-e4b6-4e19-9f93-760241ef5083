name: Deploy Landing to Production

on:
  push:
    branches: [main]
    paths:
      - "apps/landing/**"

jobs:
  deploy-landing-prod:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "apps/landing/package-lock.json"

      - name: Install dependencies
        working-directory: ./apps/landing
        run: npm ci

      - name: Run ESLint
        working-directory: ./apps/landing
        run: npm run lint

      - name: Use production credentials for Supabase
        working-directory: ./apps/landing
        run: |
          echo "VITE_SUPABASE_URL=${{ secrets.SUPABASE_URL_PROD }}" > .env
          echo "VITE_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY_PROD }}" >> .env

      - name: Build
        working-directory: ./apps/landing
        run: npm run build

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Authenticate with G<PERSON>
        uses: google-github-actions/auth@v2
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_PROD }}"
          export_environment_variables: true

      - name: Deploy to Firebase
        working-directory: ./apps/landing
        run: firebase deploy --project bizzu-33def --only hosting:bizzu
