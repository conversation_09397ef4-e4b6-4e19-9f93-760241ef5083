# Workgroup Admin Registration Flow

1. User Accesses Registration Form:

    The user receives a link to a registration form. This link includes an invitation code set as a query parameter.
1. User Fills Out the Form:

    The user fills out the registration form with their email, password, company name, and desired workgroup name (subdomain).
1. Validation:

    The frontend sends requests to the backend which triggers database functions to:
    - Validate the invitation code.
    - Check the availability of the subdomain (workgroup name).
1. User Signup:

    If the invitation code is valid and the subdomain is available, the frontend proceeds with user signup. The domain creation details are included in the user metadata. This metadata is used to determine if we need to create a new workgroup admin or add a user to a workgroup
1. Database Operations:

    Upon successful signup, a database trigger is invoked. This trigger processes the user metadata and:
    - Creates a new client record if it doesn't already exist.
    - Creates a new workgroup associated with the client.
    - Assigns the user as an admin to the newly created workgroup.
    - Marks the invitation code as used.

    Error Handling:
    If any step in the database operations fails (e.g., client creation, workgroup creation, user profile creation), the user record is deleted from the users table to maintain database integrity.

    Successful Registration:
    If all steps are completed successfully, the user is registered as an admin and is assigned to the appropriate workgroup. The registration process concludes with the user being redirected to their new application.
