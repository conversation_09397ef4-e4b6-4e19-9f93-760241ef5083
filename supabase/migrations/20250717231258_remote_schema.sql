

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."check_subdomain_availability"("subdomain" "text") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Check subdomain availability (with lock to prevent duplicates)
  PERFORM 1 FROM public.workgroups WHERE workgroups.subdomain = check_subdomain_availability.subdomain FOR UPDATE;
  
  IF FOUND THEN
    RETURN json_build_object(
      'available', false,
      'error', 'Subdomain already taken'
    );
  END IF;
  
  RETURN json_build_object('available', true);
END;
$$;


ALTER FUNCTION "public"."check_subdomain_availability"("subdomain" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_admin_profile"("user_id" "uuid", "workgroup_id" "uuid") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  INSERT INTO public.user_profiles (id, workgroup_id, role, created_at)
  VALUES (user_id, workgroup_id, 'admin', NOW());
  
  RETURN json_build_object('success', true);
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User profile already exists'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid user or workgroup ID'
    );
END;
$$;


ALTER FUNCTION "public"."create_admin_profile"("user_id" "uuid", "workgroup_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_workgroup"("subdomain" "text", "client_id" "uuid") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_workgroup_id UUID;
BEGIN
  INSERT INTO public.workgroups (id, subdomain, client_id)
  VALUES (gen_random_uuid(), subdomain, client_id)
  RETURNING id INTO new_workgroup_id;
  
  RETURN json_build_object(
    'success', true,
    'workgroup_id', new_workgroup_id
  );
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Subdomain already taken'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid client ID'
    );
END;
$$;


ALTER FUNCTION "public"."create_workgroup"("subdomain" "text", "client_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."find_or_create_client"("company_name" "text", "nip" "text") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  existing_client_id UUID;
BEGIN
  -- Check if client exists by NIP
  SELECT id INTO existing_client_id 
  FROM public.clients 
  WHERE clients.nip = find_or_create_client.nip
  FOR UPDATE;
  
  IF existing_client_id IS NULL THEN
    INSERT INTO public.clients (id, name, nip, created_at)
    VALUES (gen_random_uuid(), company_name, nip, NOW())
    RETURNING id INTO existing_client_id;
    
    RAISE LOG 'Created new client: % with NIP: %', company_name, nip;
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'client_id', existing_client_id
  );
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Company with this NIP already exists'
    );
END;
$$;


ALTER FUNCTION "public"."find_or_create_client"("company_name" "text", "nip" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_invitation_used"("invitation_id" "uuid", "user_id" "uuid") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  UPDATE public.invitation_codes 
  SET activated_date = NOW(), activated_by = user_id
  WHERE id = invitation_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invitation code not found'
    );
  END IF;
  
  RETURN json_build_object('success', true);
END;
$$;


ALTER FUNCTION "public"."mark_invitation_used"("invitation_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."register_workgroup_admin"("invitation_code" "text", "subdomain" "text", "company_name" "text", "nip" "text", "user_id" "uuid") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation_result JSON;
  subdomain_result JSON;
  client_result JSON;
  workgroup_result JSON;
  profile_result JSON;
  mark_result JSON;
  
  invitation_id UUID;
  client_id UUID;
  workgroup_id UUID;
BEGIN
  -- Start explicit transaction
  BEGIN
    SELECT validate_invitation_code(invitation_code) INTO invitation_result;
    
    IF NOT (invitation_result->>'valid')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', invitation_result->>'error'
      );
    END IF;
    
    invitation_id := (invitation_result->>'invitation_id')::uuid;
    
    SELECT check_subdomain_availability(subdomain) INTO subdomain_result;
    
    IF NOT (subdomain_result->>'available')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', subdomain_result->>'error'
      );
    END IF;
    
    SELECT find_or_create_client(company_name, nip) INTO client_result;
    
    IF NOT (client_result->>'success')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', client_result->>'error'
      );
    END IF;
    
    client_id := (client_result->>'client_id')::uuid;
    
    SELECT create_workgroup(subdomain, client_id) INTO workgroup_result;
    
    IF NOT (workgroup_result->>'success')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', workgroup_result->>'error'
      );
    END IF;
    
    workgroup_id := (workgroup_result->>'workgroup_id')::uuid;
    
    SELECT create_admin_profile(user_id, workgroup_id) INTO profile_result;
    
    IF NOT (profile_result->>'success')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', profile_result->>'error'
      );
    END IF;
    
    SELECT mark_invitation_used(invitation_id, user_id) INTO mark_result;
    
    IF NOT (mark_result->>'success')::boolean THEN
      RETURN json_build_object(
        'success', false,
        'error', mark_result->>'error'
      );
    END IF;
    
    RETURN json_build_object(
      'success', true,
      'workgroup_id', workgroup_id,
      'client_id', client_id,
      'subdomain', subdomain
    );
    
  EXCEPTION 
    WHEN OTHERS THEN
      RAISE LOG 'Registration error for user %: %', user_id, SQLERRM;
      RETURN json_build_object(
        'success', false, 
        'error', 'Registration failed: ' || SQLERRM
      );
  END;
END;
$$;


ALTER FUNCTION "public"."register_workgroup_admin"("invitation_code" "text", "subdomain" "text", "company_name" "text", "nip" "text", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_exist_in_workgroup"("user_id" "uuid", "subdomain" "text") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  
  PERFORM 1 
  FROM public.user_profiles up 
  LEFT JOIN public.workgroups wp ON wp.id = up.workgroup_id 
  WHERE wp.subdomain = user_exist_in_workgroup.subdomain AND up.id = user_exist_in_workgroup.user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'allow', false,
      'error', 'User not assigned in workgroup'
    );
  END IF;
  
  RETURN json_build_object('allow', true);
END;
$$;


ALTER FUNCTION "public"."user_exist_in_workgroup"("user_id" "uuid", "subdomain" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."validate_invitation_code"("invitation_code" "text") RETURNS json
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation_record invitation_codes;
BEGIN
  -- Lock and validate invitation code (prevents race conditions)
  SELECT * INTO invitation_record 
  FROM public.invitation_codes 
  WHERE id = invitation_code::uuid 
  AND activated_date IS NULL 
  FOR UPDATE NOWAIT;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'valid', false, 
      'error', 'Invalid or expired invitation code'
    );
  END IF;
  
  RETURN json_build_object(
    'valid', true,
    'invitation_id', invitation_record.id
  );
EXCEPTION 
  WHEN lock_not_available THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'Invitation code validation in progress, please try again'
    );
END;
$$;


ALTER FUNCTION "public"."validate_invitation_code"("invitation_code" "text") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."clients" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "nip" character varying,
    "created_at" timestamp without time zone DEFAULT "now"()
);


ALTER TABLE "public"."clients" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invitation_codes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "activated_date" timestamp without time zone,
    "activated_by" "uuid"
);


ALTER TABLE "public"."invitation_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_profiles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "workgroup_id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "role" character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workgroups" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "subdomain" "text" NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"() NOT NULL,
    "client_id" "uuid"
);


ALTER TABLE "public"."workgroups" OWNER TO "postgres";


ALTER TABLE ONLY "public"."clients"
    ADD CONSTRAINT "clients_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."clients"
    ADD CONSTRAINT "clients_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invitation_codes"
    ADD CONSTRAINT "invitation_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invitation_codes"
    ADD CONSTRAINT "registration_codes_code_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."workgroups"
    ADD CONSTRAINT "tenants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workgroups"
    ADD CONSTRAINT "tenants_subdomain_key" UNIQUE ("subdomain");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id", "workgroup_id");



ALTER TABLE ONLY "public"."invitation_codes"
    ADD CONSTRAINT "invitation_codes_activated_by_fkey" FOREIGN KEY ("activated_by") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workgroups"
    ADD CONSTRAINT "tenants_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON UPDATE CASCADE;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_workgroup_id_fkey" FOREIGN KEY ("workgroup_id") REFERENCES "public"."workgroups"("id");



CREATE POLICY "Allow All for dashboard users, admins and postgres" ON "public"."workgroups" TO "postgres", "dashboard_user", "supabase_admin" USING (true);



CREATE POLICY "Enable read access for authenticated" ON "public"."clients" FOR SELECT TO "authenticated" USING (true);



ALTER TABLE "public"."clients" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."invitation_codes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workgroups" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

























































































































































GRANT ALL ON FUNCTION "public"."check_subdomain_availability"("subdomain" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."check_subdomain_availability"("subdomain" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_subdomain_availability"("subdomain" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_admin_profile"("user_id" "uuid", "workgroup_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."create_admin_profile"("user_id" "uuid", "workgroup_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_admin_profile"("user_id" "uuid", "workgroup_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_workgroup"("subdomain" "text", "client_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."create_workgroup"("subdomain" "text", "client_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_workgroup"("subdomain" "text", "client_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."find_or_create_client"("company_name" "text", "nip" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."find_or_create_client"("company_name" "text", "nip" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_or_create_client"("company_name" "text", "nip" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_invitation_used"("invitation_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_invitation_used"("invitation_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_invitation_used"("invitation_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."register_workgroup_admin"("invitation_code" "text", "subdomain" "text", "company_name" "text", "nip" "text", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."register_workgroup_admin"("invitation_code" "text", "subdomain" "text", "company_name" "text", "nip" "text", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."register_workgroup_admin"("invitation_code" "text", "subdomain" "text", "company_name" "text", "nip" "text", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."user_exist_in_workgroup"("user_id" "uuid", "subdomain" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."user_exist_in_workgroup"("user_id" "uuid", "subdomain" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_exist_in_workgroup"("user_id" "uuid", "subdomain" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."validate_invitation_code"("invitation_code" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."validate_invitation_code"("invitation_code" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."validate_invitation_code"("invitation_code" "text") TO "service_role";


















GRANT ALL ON TABLE "public"."clients" TO "anon";
GRANT ALL ON TABLE "public"."clients" TO "authenticated";
GRANT ALL ON TABLE "public"."clients" TO "service_role";



GRANT ALL ON TABLE "public"."invitation_codes" TO "anon";
GRANT ALL ON TABLE "public"."invitation_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."invitation_codes" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."workgroups" TO "anon";
GRANT ALL ON TABLE "public"."workgroups" TO "authenticated";
GRANT ALL ON TABLE "public"."workgroups" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";






























RESET ALL;
