revoke delete on table "public"."migration_test" from "anon";

revoke insert on table "public"."migration_test" from "anon";

revoke references on table "public"."migration_test" from "anon";

revoke select on table "public"."migration_test" from "anon";

revoke trigger on table "public"."migration_test" from "anon";

revoke truncate on table "public"."migration_test" from "anon";

revoke update on table "public"."migration_test" from "anon";

revoke delete on table "public"."migration_test" from "authenticated";

revoke insert on table "public"."migration_test" from "authenticated";

revoke references on table "public"."migration_test" from "authenticated";

revoke select on table "public"."migration_test" from "authenticated";

revoke trigger on table "public"."migration_test" from "authenticated";

revoke truncate on table "public"."migration_test" from "authenticated";

revoke update on table "public"."migration_test" from "authenticated";

revoke delete on table "public"."migration_test" from "service_role";

revoke insert on table "public"."migration_test" from "service_role";

revoke references on table "public"."migration_test" from "service_role";

revoke select on table "public"."migration_test" from "service_role";

revoke trigger on table "public"."migration_test" from "service_role";

revoke truncate on table "public"."migration_test" from "service_role";

revoke update on table "public"."migration_test" from "service_role";

alter table "public"."migration_test" drop constraint "migration_test_pkey";

drop index if exists "public"."migration_test_pkey";

drop table "public"."migration_test";


