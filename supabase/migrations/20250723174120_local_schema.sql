drop function if exists "public"."create_admin_profile"(user_id uuid, workgroup_id uuid);

drop function if exists "public"."find_or_create_client"(company_name text, nip text);

drop function if exists "public"."register_workgroup_admin"(invitation_code text, subdomain text, company_name text, nip text, user_id uuid);

drop function if exists "public"."user_exist_in_workgroup"(user_id uuid, subdomain text);

alter table "public"."clients" drop column if exists "nip";

alter table "public"."user_profiles" drop column if exists "created_at";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.after_user_insert()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    user_metadata JSONB;
    workgroup_id UUID;
BEGIN
    -- Start explicit transaction
    BEGIN
        user_metadata := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);

        IF user_metadata ? 'domainCreation' THEN
            BEGIN
                PERFORM public.register_workgroup_admin(
                    user_metadata->'domainCreation'->>'invitationCode'::TEXT,
                    user_metadata->'domainCreation'->>'subdomain'::TEXT,
                    user_metadata->'domainCreation'->>'companyName'::TEXT,
                    NEW.id
                );
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSIF user_metadata ? 'userCreation' THEN
            BEGIN
                SELECT id INTO workgroup_id
                FROM public.workgroups
                WHERE subdomain = user_metadata->'userCreation'->>'subdomain';  

                PERFORM public.create_user_profile(
                    NEW.id,
                    workgroup_id,
                    user_metadata->'userCreation'->>'role'::TEXT
                );
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSE 
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE EXCEPTION 'Invalid user metadata';
        END IF;

        RETURN NEW;
    EXCEPTION
        WHEN OTHERS THEN
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE;
    END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_user_profile(user_id uuid, workgroup_id uuid, role text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.user_profiles (id, workgroup_id, role)
  VALUES (user_id, workgroup_id, role);
  
  RETURN json_build_object('success', true);
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User profile already exists'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid user or workgroup ID'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.find_or_create_client(company_name text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  existing_client_id UUID;
BEGIN
  -- Check if client exists by company name
  SELECT id INTO existing_client_id 
  FROM public.clients 
  WHERE clients.name = find_or_create_client.company_name
  FOR UPDATE;
  
  IF existing_client_id IS NULL THEN
    INSERT INTO public.clients (id, name, created_at)
    VALUES (gen_random_uuid(), company_name, NOW())
    RETURNING id INTO existing_client_id;
    
    RAISE LOG 'Created new client: %', company_name;
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'client_id', existing_client_id
  );
  
END;
$function$
;

CREATE OR REPLACE FUNCTION public.register_workgroup_admin(invitation_code text, subdomain text, company_name text, user_id uuid)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  client_result JSON;
  workgroup_result JSON;
  profile_result JSON;
  mark_result JSON;
  client_id UUID;
  workgroup_id UUID;
BEGIN
  -- Start explicit transaction
  BEGIN  
    SELECT public.find_or_create_client(company_name) INTO client_result;
    
    IF NOT (client_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Client creation failed %', client_result->>'error';
    END IF;
    
    client_id := (client_result->>'client_id')::uuid;
    
    SELECT public.create_workgroup(subdomain, client_id) INTO workgroup_result;
    
    IF NOT (workgroup_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Workgroup creation failed %', workgroup_result->>'error';
    END IF;
    
    workgroup_id := (workgroup_result->>'workgroup_id')::uuid;
    
    SELECT public.create_user_profile(user_id, workgroup_id, 'admin') INTO profile_result;
    
    IF NOT (profile_result->>'success')::boolean THEN
      RAISE EXCEPTION 'User profile creation failed %', profile_result->>'error';
    END IF;
    
    SELECT public.mark_invitation_used(invitation_code::UUID, user_id) INTO mark_result;
    
    IF NOT (mark_result->>'success')::boolean THEN
      RAISE EXCEPTION 'Invitation code activation failed %', mark_result->>'error';
    END IF;
    
    RAISE NOTICE 'Successfully registered workgroup admin for user %', user_id;

    RETURN json_build_object(
        'success', true,
        'message', 'Successfully registered workgroup admin',
        'workgroup_id', workgroup_id,
        'client_id', client_id,
        'user_id', user_id
      );
  EXCEPTION 
    WHEN OTHERS THEN
      RAISE LOG 'Registration error for user %: %', user_id, SQLERRM;
      RAISE;
  END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.user_exists_in_workgroup(user_id uuid, subdomain text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  
  PERFORM 1 
  FROM public.user_profiles up 
  LEFT JOIN public.workgroups wp ON wp.id = up.workgroup_id 
  WHERE wp.subdomain = user_exists_in_workgroup.subdomain AND up.id = user_exists_in_workgroup.user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'allow', false,
      'error', 'User not assigned in workgroup'
    );
  END IF;
  
  RETURN json_build_object('allow', true);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_workgroup(subdomain text, client_id uuid)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  new_workgroup_id UUID;
BEGIN
  INSERT INTO public.workgroups (id, subdomain, client_id)
  VALUES (gen_random_uuid(), subdomain, client_id)
  RETURNING id INTO new_workgroup_id;
  
  RETURN json_build_object(
    'success', true,
    'workgroup_id', new_workgroup_id
  );

EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Subdomain already taken'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid client ID'
    );
END;

$function$
;

create policy "clients_select"
on "public"."clients"
as permissive
for select
to public
using ((id IN ( SELECT w.client_id
   FROM (workgroups w
     JOIN user_profiles up ON ((w.id = up.workgroup_id)))
  WHERE (up.id = auth.uid()))));



