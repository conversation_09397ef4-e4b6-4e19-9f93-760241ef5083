CREATE OR REPLACE FUNCTION mark_invitation_used(invitation_id UUID, user_id UUID)
RETURNS JSON AS $$
BEGIN
  UPDATE public.invitation_codes 
  SET activated_date = NOW(), activated_by = user_id
  WHERE id = invitation_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invitation code not found'
    );
  END IF;
  
  RETURN json_build_object('success', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
