CREATE OR REPLACE FUNCTION create_user_profile(user_id UUID, workgroup_id UUID, role TEXT)
RETURNS JSON AS $$
BEGIN
  INSERT INTO public.user_profiles (id, workgroup_id, role)
  VALUES (user_id, workgroup_id, role);
  
  RETURN json_build_object('success', true);
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User profile already exists'
    );
  WHEN foreign_key_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid user or workgroup ID'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
