CREATE OR REPLACE FUNCTION user_exists_in_workgroup(user_id UUID, subdomain TEXT)
RETURNS JSON AS $$
BEGIN
  
  PERFORM 1 
  FROM public.user_profiles up 
  LEFT JOIN public.workgroups wp ON wp.id = up.workgroup_id 
  WHERE wp.subdomain = user_exists_in_workgroup.subdomain AND up.id = user_exists_in_workgroup.user_id;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'allow', false,
      'error', 'User not assigned in workgroup'
    );
  END IF;
  
  RETURN json_build_object('allow', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
