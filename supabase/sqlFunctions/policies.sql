--  TODO
-- implement that into db
-- check if that works
-- but what if we need to restrict user to modify only his own data - eg on user profile?

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(
  user_id UUID,
  resource_name TEXT,
  action_name TEXT,
  target_workgroup_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
  user_workgroup_id UUID;
BEGIN
  SELECT role, workgroup_id INTO user_role, user_workgroup_id
  FROM user_profiles 
  WHERE id = user_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- If target_workgroup_id is specified, ensure user belongs to that workgroup
  IF target_workgroup_id IS NOT NULL AND user_workgroup_id != target_workgroup_id THEN
    RETURN FALSE;
  END IF;
  
  -- Check if permission exists for user's role
  RETURN EXISTS (
    SELECT 1 FROM permissions 
    WHERE resource = resource_name 
    AND role = user_role 
    AND action = action_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all user permissions (useful for frontend)
CREATE OR REPLACE FUNCTION get_user_permissions(user_id UUID)
RETURNS TABLE(resource TEXT, action TEXT) AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Get user's role
  SELECT up.role INTO user_role
  FROM user_profiles up
  WHERE up.id = user_id;
  
  IF NOT FOUND THEN
    RETURN;
  END IF;
  
  -- Return all permissions for this role
  RETURN QUERY
  SELECT p.resource, p.action
  FROM permissions p
  WHERE p.role = user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;



--  CHECK POLICIES

-- Workgroups: Users can only see their own workgroup
CREATE POLICY workgroup_access ON workgroups
  FOR ALL USING (
    id IN (
      SELECT workgroup_id FROM user_profiles 
      WHERE id = auth.uid()
    )
  );

-- User profiles: Users can see profiles in their workgroup + permission check
CREATE POLICY user_profiles_select ON user_profiles
  FOR SELECT USING (
    workgroup_id IN (
      SELECT workgroup_id FROM user_profiles 
      WHERE id = auth.uid()
    )
    AND user_has_permission(auth.uid(), 'users', 'read')
  );

CREATE POLICY user_profiles_insert ON user_profiles
  FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'users', 'create', workgroup_id)
  );

CREATE POLICY user_profiles_update ON user_profiles
  FOR UPDATE USING (
    user_has_permission(auth.uid(), 'users', 'update', workgroup_id)
  );

CREATE POLICY user_profiles_delete ON user_profiles
  FOR DELETE USING (
    user_has_permission(auth.uid(), 'users', 'delete', workgroup_id)
  );

