CREATE OR REPLACE FUNCTION after_user_insert()
R<PERSON>URNS TRIGGER AS $$
DECLARE
    user_metadata JSONB;
    workgroup_id UUID;
BEGIN
    -- Start explicit transaction
    BEGIN
        user_metadata := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);

        IF user_metadata ? 'domainCreation' THEN
            BEGIN
                PERFORM public.register_workgroup_admin(
                    user_metadata->'domainCreation'->>'invitationCode'::TEXT,
                    user_metadata->'domainCreation'->>'subdomain'::TEXT,
                    user_metadata->'domainCreation'->>'companyName'::TEXT,
                    NEW.id
                );
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSIF user_metadata ? 'userCreation' THEN
            BEGIN
                SELECT id INTO workgroup_id
                FROM public.workgroups
                WHERE subdomain = user_metadata->'userCreation'->>'subdomain';  

                PERFORM public.create_user_profile(
                    NEW.id,
                    workgroup_id,
                    user_metadata->'userCreation'->>'role'::TEXT
                );
            EXCEPTION
                WHEN OTHERS THEN
                    DELETE FROM auth.users WHERE id = NEW.id;
                    RAISE;
            END;
        ELSE 
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE EXCEPTION 'Invalid user metadata';
        END IF;

        RETURN NEW;
    EXCEPTION
        WHEN OTHERS THEN
            DELETE FROM auth.users WHERE id = NEW.id;
            RAISE;
    END;
END;
$$ LANGUAGE plpgsql;
