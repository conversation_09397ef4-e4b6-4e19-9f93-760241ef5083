CREATE OR R<PERSON>LACE FUNCTION validate_invitation_code(invitation_code TEXT)
RETURNS JSON AS $$
DECLARE
  invitation_record invitation_codes;
BEGIN
  -- Lock and validate invitation code (prevents race conditions)
  SELECT * INTO invitation_record 
  FROM public.invitation_codes 
  WHERE id = invitation_code::uuid 
  AND activated_date IS NULL 
  FOR UPDATE NOWAIT;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'valid', false, 
      'error', 'Invalid or expired invitation code'
    );
  END IF;
  
  RETURN json_build_object(
    'valid', true,
    'invitation_id', invitation_record.id
  );
EXCEPTION 
  WHEN lock_not_available THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'Invitation code validation in progress, please try again'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to anon users
GRANT EXECUTE ON FUNCTION validate_invitation_code(TEXT) TO anon;
