CREATE OR REPLACE FUNCTION check_subdomain_availability(subdomain TEXT)
RETURNS JSON AS $$
BEGIN
  -- Check subdomain availability (with lock to prevent duplicates)
  PERFORM 1 FROM public.workgroups WHERE workgroups.subdomain = check_subdomain_availability.subdomain FOR UPDATE;
  
  IF FOUND THEN
    RETURN json_build_object(
      'available', false,
      'error', 'Subdomain already taken'
    );
  END IF;
  
  RETURN json_build_object('available', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to anon users
GRANT EXECUTE ON FUNCTION check_subdomain_availability(TEXT) TO anon;
