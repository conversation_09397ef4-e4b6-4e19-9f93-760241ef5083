CREATE OR REPLACE FUNCTION find_or_create_client(company_name TEXT)
RETURNS JSON AS $$
DECLARE
  existing_client_id UUID;
BEGIN
  -- Check if client exists by company name
  SELECT id INTO existing_client_id 
  FROM public.clients 
  WHERE clients.name = find_or_create_client.company_name
  FOR UPDATE;
  
  IF existing_client_id IS NULL THEN
    INSERT INTO public.clients (id, name, created_at)
    VALUES (gen_random_uuid(), company_name, NOW())
    RETURNING id INTO existing_client_id;
    
    RAISE LOG 'Created new client: %', company_name;
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'client_id', existing_client_id
  );
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
